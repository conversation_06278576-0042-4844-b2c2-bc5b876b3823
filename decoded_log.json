{"sign": "4ed86afaa1a96559fd9715820c3862cca2ca70f9", "game_id": "", "page_info": [{"name": "RedCrystal.RCCommonVC", "time": "1746988875", "step": "viewWillAppear"}, {"name": "RedCrystal.RCCommonVC", "time": "1746988878", "step": "viewWillDisappear"}, {"name": "RedCrystal.RCCommonVC", "time": "1746988928", "step": "viewWillAppear"}, {"name": "RedCrystal.RCCommonVC", "time": "1746988930", "step": "viewWillDisappear"}, {"name": "UnityDefaultViewController", "time": "1746989024", "step": "viewWillDisappear"}, {"name": "UIInputWindowController", "time": "1747016737", "step": "viewWillAppear"}, {"name": "UISystemKeyboardDockController", "time": "1747016737", "step": "viewWillAppear"}, {"name": "UIEditingOverlayViewController", "time": "1747016737", "step": "viewWillAppear"}, {"name": "UISystemKeyboardDockController", "time": "1747016737", "step": "viewDidAppear"}, {"name": "UIInputWindowController", "time": "1747016737", "step": "viewDidAppear"}], "extra_app_id": "11", "singular_type": 7, "uuid_info": [{"name": "customError", "uuid": "1d16797f7f204fab85aaa5b3e25b6cde"}], "detail_info": {"os_type": 2, "is_emulator": 0, "sdk_package_name": "com.ysczg.yuns.cheng", "ts": "1747019232", "app_name": "云上城之歌", "app_version": "3.0.18", "network_type": "4G", "crash_process": "", "channel_source": "SHIYUE", "cpu_framework": "arm64", "server_dev_str": "1c3c8a2fe32e52815b18de29e4720add60e75790", "client_ts": "1747019231", "resolution": "2778x1284", "rom_info": "", "around_status": 1, "sdk_ver": "", "operator_name": "--", "app_id": "", "crash_plugin_ver": "2.2.8", "is_prison_break": 0, "os_version": "18.4.1", "device_model": "iPhone 13 Pro Max", "time_zone": "GMT+8", "rhsdk_ver": "", "use_duration": "", "system_region": "CN", "device_brand": "Apple"}, "singular_explain": "Failed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$attack7005.anim.single'.", "duration_time": 1, "console_info": [{"details": "(Segmented%20read)%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.320%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.322%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.351%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.487%20ProductName%5B92702:10875802%5D%20Unable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$ridestand7001.anim.single%0AUnable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$ridestand7001.anim.single%0A2025-05-12%2011:06:27.487%20ProductName%5B92702:10875804%5D%20Unable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$dead7001.anim.single%0AUnable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$dead7001.anim.single%0A2025-05-12%2011:06:27.487%20ProductName%5B92702:10875802%5D%20Unable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$idle7001.anim.single%0AUnable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$idle7001.anim.single%0A2025-05-12%2011:06:27.487%20ProductName%5B92702:10875805%5D%20Unable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$battlestand7001.anim.single%0AUnable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$battlestand7001.anim.single%0A2025-05-12%2011:06:27.487%20ProductName%5B92702:10875804%5D%20Unable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$move7001.anim.single%0AUnable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$move7001.anim.single%0A2025-05-12%2011:06:27.487%20ProductName%5B92702:10875803%5D%20Unable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$battlemove7001.anim.single%0AUnable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$battlemove7001.anim.single%0A2025-05-12%2011:06:27.516%20ProductName%5B92702:10875765%5D%20Failed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$ridestand7001.anim.single'.%0AFailed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$ridestand7001.anim.single'.%0A2025-05-12%2011:06:27.516%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20AssetBundle.LoadFromFile%C3%82%C3%A1%E2%88%AB%C3%88%C3%AE%C3%B4%E2%80%B0%E2%88%AB%C3%9C%C3%94%C2%BA%C3%B6%5Bab%E2%80%B0%E2%88%8F%E2%88%AB%C3%81%C2%A9%E2%88%AB%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$ridestand7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20AssetBundle.LoadFromFile%E5%87%BA%E9%94%99%E4%BA%86%EF%BC%9A%5Bab%E4%B8%BA%E7%A9%BA%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$ridestand7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.516%20ProductName%5B92702:10875765%5D%20Failed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$dead7001.anim.single'.%0AFailed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$dead7001.anim.single'.%0A2025-05-12%2011:06:27.516%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20AssetBundle.LoadFromFile%C3%82%C3%A1%E2%88%AB%C3%88%C3%AE%C3%B4%E2%80%B0%E2%88%AB%C3%9C%C3%94%C2%BA%C3%B6%5Bab%E2%80%B0%E2%88%8F%E2%88%AB%C3%81%C2%A9%E2%88%AB%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$dead7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20AssetBundle.LoadFromFile%E5%87%BA%E9%94%99%E4%BA%86%EF%BC%9A%5Bab%E4%B8%BA%E7%A9%BA%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$dead7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.516%20ProductName%5B92702:10875765%5D%20Failed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$idle7001.anim.single'.%0AFailed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$idle7001.anim.single'.%0A2025-05-12%2011:06:27.516%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20AssetBundle.LoadFromFile%C3%82%C3%A1%E2%88%AB%C3%88%C3%AE%C3%B4%E2%80%B0%E2%88%AB%C3%9C%C3%94%C2%BA%C3%B6%5Bab%E2%80%B0%E2%88%8F%E2%88%AB%C3%81%C2%A9%E2%88%AB%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$idle7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20AssetBundle.LoadFromFile%E5%87%BA%E9%94%99%E4%BA%86%EF%BC%9A%5Bab%E4%B8%BA%E7%A9%BA%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$idle7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.517%20ProductName%5B92702:10875765%5D%20Failed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$battlestand7001.anim.single'.%0AFailed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$battlestand7001.anim.single'.%0A2025-05-12%2011:06:27.517%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20AssetBundle.LoadFromFile%C3%82%C3%A1%E2%88%AB%C3%88%C3%AE%C3%B4%E2%80%B0%E2%88%AB%C3%9C%C3%94%C2%BA%C3%B6%5Bab%E2%80%B0%E2%88%8F%E2%88%AB%C3%81%C2%A9%E2%88%AB%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$battlestand7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20AssetBundle.LoadFromFile%E5%87%BA%E9%94%99%E4%BA%86%EF%BC%9A%5Bab%E4%B8%BA%E7%A9%BA%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$battlestand7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.517%20ProductName%5B92702:10875765%5D%20Failed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$move7001.anim.single'.%0AFailed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$move7001.anim.single'.%0A2025-05-12%2011:06:27.517%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20AssetBundle.LoadFromFile%C3%82%C3%A1%E2%88%AB%C3%88%C3%AE%C3%B4%E2%80%B0%E2%88%AB%C3%9C%C3%94%C2%BA%C3%B6%5Bab%E2%80%B0%E2%88%8F%E2%88%AB%C3%81%C2%A9%E2%88%AB%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$move7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20AssetBundle.LoadFromFile%E5%87%BA%E9%94%99%E4%BA%86%EF%BC%9A%5Bab%E4%B8%BA%E7%A9%BA%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$move7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.517%20ProductName%5B92702:10875765%5D%20Failed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$battlemove7001.anim.single'.%0AFailed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$battlemove7001.anim.single'.%0A2025-05-12%2011:06:27.517%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20AssetBundle.LoadFromFile%C3%82%C3%A1%E2%88%AB%C3%88%C3%AE%C3%B4%E2%80%B0%E2%88%AB%C3%9C%C3%94%C2%BA%C3%B6%5Bab%E2%80%B0%E2%88%8F%E2%88%AB%C3%81%C2%A9%E2%88%AB%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$battlemove7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20AssetBundle.LoadFromFile%E5%87%BA%E9%94%99%E4%BA%86%EF%BC%9A%5Bab%E4%B8%BA%E7%A9%BA%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$battlemove7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.618%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.619%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.621%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.622%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.623%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.651%20ProductName%5B92702:10875800%5D%20Unable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$rideon5101.anim.single%0AUnable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$rideon5101.anim.single%0A2025-05-12%2011:06:27.719%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.719%20ProductName%5B92702:10875765%5D%20Failed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$rideon5101.anim.single'.%0AFailed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$rideon5101.anim.single'.%0A2025-05-12%2011:06:27.720%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20AssetBundle.LoadFromFile%C3%82%C3%A1%E2%88%AB%C3%88%C3%AE%C3%B4%E2%80%B0%E2%88%AB%C3%9C%C3%94%C2%BA%C3%B6%5Bab%E2%80%B0%E2%88%8F%E2%88%AB%C3%81%C2%A9%E2%88%AB%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$rideon5101.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20AssetBundle.LoadFromFile%E5%87%BA%E9%94%99%E4%BA%86%EF%BC%9A%5Bab%E4%B8%BA%E7%A9%BA%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$rideon5101.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.818%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.823%20ProductName%5B92702:10875800%5D%20Unable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$rideon7001.anim.single%0AUnable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$rideon7001.anim.single%0A2025-05-12%2011:06:27.849%20ProductName%5B92702:10875765%5D%20Failed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$rideon7001.anim.single'.%0AFailed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$rideon7001.anim.single'.%0A2025-05-12%2011:06:27.849%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20AssetBundle.LoadFromFile%C3%82%C3%A1%E2%88%AB%C3%88%C3%AE%C3%B4%E2%80%B0%E2%88%AB%C3%9C%C3%94%C2%BA%C3%B6%5Bab%E2%80%B0%E2%88%8F%E2%88%AB%C3%81%C2%A9%E2%88%AB%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$rideon7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20AssetBundle.LoadFromFile%E5%87%BA%E9%94%99%E4%BA%86%EF%BC%9A%5Bab%E4%B8%BA%E7%A9%BA%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$rideon7001.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:27.952%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:30.842%20ProductName%5B92702:10875900%5D%20Unloading%201%20Unused%20Serialized%20files%20(Serialized%20files%20now%20loaded:%201580)%0AUnloading%201%20Unused%20Serialized%20files%20(Serialized%20files%20now%20loaded:%201580)%0A2025-05-12%2011:06:30.923%20ProductName%5B92702:10875765%5D%20Unloading%2092%20unused%20Assets%20to%20reduce%20memory%20usage.%20Loaded%20Objects%20now:%2089351.%0AUnloading%2092%20unused%20Assets%20to%20reduce%20memory%20usage.%20Loaded%20Objects%20now:%2089351.%0A2025-05-12%2011:06:30.923%20ProductName%5B92702:10875765%5D%20Total:%2068.846000%20ms%20(FindLiveObjects:%203.651167%20ms%20CreateObjectMapping:%201.599625%20ms%20MarkObjects:%2063.281541%20ms%20%20DeleteObjects:%200.313416%20ms)%0A%0ATotal:%2068.846000%20ms%20(FindLiveObjects:%203.651167%20ms%20CreateObjectMapping:%201.599625%20ms%20MarkObjects:%2063.281541%20ms%20%20DeleteObjects:%200.313416%20ms)%0A%0A2025-05-12%2011:06:31.085%20ProductName%5B92702:10875803%5D%20Unable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/effect\\/texture\\/water\\/effect$texture$water$romi_shuihua_002.tga.single%0A05\\/12%2011:06:31.016%7CU%7C20314%7CGME%20%20%20%20%7Cav_context_proxy.cpp(554):Poll%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7C%5BAPI%5DGMEPoll:%5B205%5D%0A%0AUnable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/effect\\/texture\\/water\\/effect$texture$water$romi_shuihua_002.tga.single%0A2025-05-12%2011:06:31.152%20ProductName%5B92702:10875765%5D%20Failed%20to%20read%20data%20for%20the%20AssetBundle%20'effect$texture$water$romi_shuihua_002.tga.single'.%0AFailed%20to%20read%20data%20for%20the%20AssetBundle%20'effect$texture$water$romi_shuihua_002.tga.single'.%0A2025-05-12%2011:06:31.152%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20AssetBundle.LoadFromFile%C3%82%C3%A1%E2%88%AB%C3%88%C3%AE%C3%B4%E2%80%B0%E2%88%AB%C3%9C%C3%94%C2%BA%C3%B6%5Bab%E2%80%B0%E2%88%8F%E2%88%AB%C3%81%C2%A9%E2%88%AB%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/effect\\/texture\\/water\\/effect$texture$water$romi_shuihua_002.tga.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20AssetBundle.LoadFromFile%E5%87%BA%E9%94%99%E4%BA%86%EF%BC%9A%5Bab%E4%B8%BA%E7%A9%BA%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/effect\\/texture\\/water\\/effect$texture$water$romi_shuihua_002.tga.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:06:31.217%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:07:11.327%20ProductName%5B92702:10875800%5D%20Unable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$attack7005.anim.single%0A05\\/12%2011:06:41.085%7CU%7C20314%7CGME%20%20%20%20%7Cav_context_proxy.cpp(554):Poll%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7C%5BAPI%5DGMEPoll:%5B206%5D%0A%0A05\\/12%2011:06:51.118%7CU%7C20314%7CGME%20%20%20%20%7Cav_context_proxy.cpp(554):Poll%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7C%5BAPI%5DGMEPoll:%5B207%5D%0A%0A05\\/12%2011:07:01.157%7CU%7C20314%7CGME%20%20%20%20%7Cav_context_proxy.cpp(554):Poll%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7C%5BAPI%5DGMEPoll:%5B208%5D%0A%0A05\\/12%2011:07:11.195%7CU%7C20314%7CGME%20%20%20%20%7Cav_context_proxy.cpp(554):Poll%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7C%5BAPI%5DGMEPoll:%5B209%5D%0A%0AUnable%20to%20open%20archive%20file:%20\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$attack7005.anim.single%0A2025-05-12%2011:07:11.352%20ProductName%5B92702:10875765%5D%20Failed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$attack7005.anim.single'.%0AFailed%20to%20read%20data%20for%20the%20AssetBundle%20'unit$role$animation$attack7005.anim.single'.%0A2025-05-12%2011:07:11.352%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20AssetBundle.LoadFromFile%C3%82%C3%A1%E2%88%AB%C3%88%C3%AE%C3%B4%E2%80%B0%E2%88%AB%C3%9C%C3%94%C2%BA%C3%B6%5Bab%E2%80%B0%E2%88%8F%E2%88%AB%C3%81%C2%A9%E2%88%AB%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$attack7005.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20AssetBundle.LoadFromFile%E5%87%BA%E9%94%99%E4%BA%86%EF%BC%9A%5Bab%E4%B8%BA%E7%A9%BA%5D\\/private\\/var\\/containers\\/Bundle\\/Application\\/9A52285E-0C8D-4A6B-9B28-3009D22600A8\\/ProductName.app\\/Data\\/Raw\\/unit\\/role\\/animation\\/unit$role$animation$attack7005.anim.single%0D%0A%5B\\/ERROR%5D%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A2025-05-12%2011:07:11.424%20ProductName%5B92702:10875765%5D%20%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A%5BERROR%5D%20SubpackExtFileManager%20WriteAll%20Error:Sharing%20violation%20on%20path%20\\/var\\/mobile\\/Containers\\/Data\\/Application\\/5F6EC1DB-9B0B-47FC-9772-E159F4088BD7\\/Documents\\/res\\/_subpackfile.tag%0D%0A%5B\\/ERROR%5D%0ASubpackExtFileManager:WriteAll(List%601)%0AGame.Asset.%3CDaemonLoadBundle%3Ed__35:MoveNext()%0AUnityEngine.SetupCoroutine:InvokeMoveNext(IEnumerator,%20IntPtr)%0A%0A", "level": "Info", "shops": 0, "time": "1970-01-21 13:16:59", "thread_info": "logcat"}], "origin_stacks": [{"thread_name": "错误信息", "stacks": ["SY.HitBug.CrashListener.ReportException(String, String, String, Boolean)\\nSY.HitBug.CrashListener.HandleLog(LogLevel, String, String, String)\\n"], "thread_no": 0}], "singular_name": "UnityError", "account_id": "", "memory_info": {"total_storage": ************, "available_system_memory": "", "available_memory": **********, "available_storage": ***********, "totole_data_list_memory": "", "available_data_list_memory": "", "app_available_memory": **********, "total_system_memory": "", "app_used_memory": **********, "total_memory": **********}, "content": ""}