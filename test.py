import json
import re

def decode_custom_escaped_json_string(escaped_str):
    """
    解码包含 \\xHH 类型转义序列的 JSON 字符串。

    这种格式的特点是：
    1. 双引号被转义为 \\x22。
    2. 非 ASCII 字符（如中文）的 UTF-8 字节被转义为 \\xHH\\xHH\\xHH...

    Args:
        escaped_str (str): 包含自定义转义的原始字符串。

    Returns:
        str: 格式化后的、人类可读的 JSON 字符串。
             如果解码或解析失败，则返回错误信息。
    """
    try:
        # 步骤 1: 将 \x22 转换为双引号 "
        json_string = escaped_str.replace('\\x22', '"')

        # 步骤 2: 处理所有可能的十六进制转义序列
        # 查找连续的 \xHH 序列并尝试将其解码为 UTF-8 字符
        def decode_hex_sequence(match):
            hex_sequence = match.group(0)
            # 移除 \x 前缀并连接 hex 值
            hex_values = hex_sequence.replace('\\x', '')

            try:
                # 转换为字节并解码为 UTF-8
                byte_data = bytes.fromhex(hex_values)
                decoded_text = byte_data.decode('utf-8')
                return decoded_text
            except:
                # 如果解码失败，保持原样
                return match.group(0)

        # 查找连续的 \xHH 序列（至少两个连续的十六进制转义，可能是中文字符）
        pattern = r'((?:\\x[0-9A-Fa-f]{2}){2,})'
        json_string = re.sub(pattern, lambda m: decode_hex_sequence(m), json_string)

        # 步骤 3: 处理路径中的反斜杠，将 \\ 替换为 \\\\
        # 这是因为JSON中反斜杠需要双重转义
        # 只替换那些不是有效JSON转义序列的反斜杠
        json_string = re.sub(r'\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})', r'\\\\', json_string)

        # 步骤 4: 处理单个的十六进制转义序列
        def replace_single_hex_escape(match):
            hex_val = match.group(1)
            try:
                # 将十六进制值转换为字节，然后解码为UTF-8
                byte_data = bytes.fromhex(hex_val)
                return byte_data.decode('utf-8')
            except:
                # 如果解码失败，保持原样
                return match.group(0)

        # 查找单个的十六进制转义序列
        json_string = re.sub(r'\\x([0-9A-Fa-f]{2})', replace_single_hex_escape, json_string)

        # 步骤 5: 将解码后的字符串解析为 Python 对象（字典或列表）
        python_object = json.loads(json_string)

        # 步骤 6: 递归处理嵌套的字符串值中的转义序列
        def process_nested_strings(obj):
            if isinstance(obj, dict):
                # 处理字典的键
                new_dict = {}
                for key, value in obj.items():
                    # 处理键中的转义序列（如果键是字符串）
                    if isinstance(key, str) and '\\x' in key:
                        # 处理键中的十六进制转义序列
                        new_key = key
                        # 处理连续的十六进制转义序列
                        pattern = r'((?:\\x[0-9A-Fa-f]{2}){2,})'
                        new_key = re.sub(pattern, lambda m: decode_hex_sequence(m), new_key)
                        # 处理单个的十六进制转义序列
                        new_key = re.sub(r'\\x([0-9A-Fa-f]{2})', replace_single_hex_escape, new_key)
                    else:
                        new_key = key

                    # 处理值
                    if isinstance(value, str) and '\\x' in value:
                        # 处理字符串中的十六进制转义序列
                        new_value = value
                        # 处理连续的十六进制转义序列
                        pattern = r'((?:\\x[0-9A-Fa-f]{2}){2,})'
                        new_value = re.sub(pattern, lambda m: decode_hex_sequence(m), new_value)
                        # 处理单个的十六进制转义序列
                        new_value = re.sub(r'\\x([0-9A-Fa-f]{2})', replace_single_hex_escape, new_value)
                        new_dict[new_key] = new_value
                    elif isinstance(value, (dict, list)):
                        # 递归处理嵌套的字典或列表
                        new_dict[new_key] = process_nested_strings(value)
                    else:
                        new_dict[new_key] = value
                return new_dict

            elif isinstance(obj, list):
                # 处理列表
                new_list = []
                for item in obj:
                    if isinstance(item, str) and '\\x' in item:
                        # 处理字符串中的十六进制转义序列
                        new_item = item
                        # 处理连续的十六进制转义序列
                        pattern = r'((?:\\x[0-9A-Fa-f]{2}){2,})'
                        new_item = re.sub(pattern, lambda m: decode_hex_sequence(m), new_item)
                        # 处理单个的十六进制转义序列
                        new_item = re.sub(r'\\x([0-9A-Fa-f]{2})', replace_single_hex_escape, new_item)
                        new_list.append(new_item)
                    elif isinstance(item, (dict, list)):
                        # 递归处理嵌套的字典或列表
                        new_list.append(process_nested_strings(item))
                    else:
                        new_list.append(item)
                return new_list

            else:
                # 如果不是字典或列表，直接返回
                return obj

        # 处理嵌套的字符串值
        python_object = process_nested_strings(python_object)

        # 步骤 7: 将 Python 对象转换回格式化的 JSON 字符串
        #         indent=4 用于美化输出（4个空格缩进）
        #         ensure_ascii=False 确保中文字符等能正确显示，而不是被转义为 \uXXXX
        pretty_json_string = json.dumps(python_object, indent=4, ensure_ascii=False)

        return pretty_json_string

    except json.JSONDecodeError as e:
        return f"JSON 解析错误: {e}\n解码后的内容（可能不完整或仍有问题）:\n{json_string}"
    except Exception as e:
        return f"发生未知错误: {e}"

# --- 主程序入口 ---
if __name__ == "__main__":
    # 将你提供的原始字符串粘贴到这里
    # 注意：Python 字符串中的反斜杠本身可能需要转义，
    # 但如果你的字符串是直接从某个地方复制粘贴的，它可能已经是“原始”的了。
    # 为了确保与你提供的格式一致，我们使用原始字符串字面量 (r"...")
    # 或者直接将你的示例字符串中的 \ 替换为 \\ (如果它不是原始字符串)

    # 当你从文本文件或日志中复制时，通常是这种形式
    # 从 error_log.txt 文件中读取内容
    try:
        with open('error_log.txt', 'r', encoding='utf-8') as file:
            original_ugly_string = file.read().strip()
    except Exception as e:
        print(f"读取文件时出错: {e}")
        exit(1)

    converted_string = decode_custom_escaped_json_string(original_ugly_string)

    print("转换后的字符串:")
    print(converted_string)

    # 将转换后的JSON保存到文件
    try:
        with open('decoded_log.json', 'w', encoding='utf-8') as file:
            file.write(converted_string)
        print("\n转换后的JSON已保存到 decoded_log.json 文件")
    except Exception as e:
        print(f"\n保存文件时出错: {e}")