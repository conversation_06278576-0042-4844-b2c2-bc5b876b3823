<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JavaScript错误演示</title>
    <style>
      :root {
        --primary-color: #007bff; /* 蓝色主色调 */
        --primary-hover-color: #0056b3;
        --error-color: #dc3545; /* 错误红色 */
        --error-bg-color: #f8d7da;
        --light-gray: #f8f9fa;
        --medium-gray: #dee2e6;
        --dark-gray: #343a40;
        --text-color: #212529;
        --border-radius: 6px; /* 统一圆角 */
        --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      body {
        font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
          sans-serif; /* 现代字体栈 */
        max-width: 800px;
        margin: 20px auto; /* 增加上下外边距 */
        padding: 30px; /* 增加内边距 */
        background-color: var(--light-gray); /* 浅灰背景 */
        color: var(--text-color);
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow); /* 给内容区域加阴影 */
      }

      h1 {
        text-align: center;
        color: var(--dark-gray); /* 深灰色标题 */
        margin-bottom: 30px; /* 增加标题下间距 */
      }

      .button-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); /* 调整最小宽度 */
        gap: 15px; /* 增加间隙 */
        margin-top: 20px;
      }

      button {
        padding: 12px 15px; /* 调整内边距 */
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: var(--border-radius);
        cursor: pointer;
        font-size: 1rem; /* 明确字体大小 */
        transition: background-color 0.2s ease-in-out, transform 0.1s ease, box-shadow 0.2s ease; /* 平滑过渡 */
        box-shadow: var(--box-shadow);
      }

      button:hover {
        background-color: var(--primary-hover-color);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* 悬停时阴影加深 */
      }

      button:active {
        transform: translateY(1px); /* 点击下沉效果 */
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* 点击时阴影变浅 */
      }

      .error-display {
        margin-top: 30px; /* 增加上外边距 */
        padding: 15px; /* 增加内边距 */
        border: 1px solid var(--medium-gray);
        border-radius: var(--border-radius);
        background-color: white; /* 白色背景 */
        min-height: 120px; /* 增加最小高度 */
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05); /* 内阴影 */
        position: relative; /* 为了绝对定位初始提示 */
        overflow: hidden; /* 隐藏可能溢出的内容 */
      }

      .error-display p {
        /* 调整提示文字样式 */
        margin: 0; /* 移除默认margin */
        color: #6c757d; /* 灰色提示文字 */
        font-style: italic;
        position: absolute; /* 绝对定位 */
        top: 15px; /* 与padding一致 */
        left: 15px;
        transition: opacity 0.3s ease; /* 添加淡出效果 */
      }

      .error-message {
        color: var(--error-color);
        font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace; /* 更现代的等宽字体 */
        white-space: pre-wrap; /* 允许自动换行 */
        word-break: break-all; /* 强制长单词换行 */
        font-size: 0.9rem; /* 稍小字体 */
        line-height: 1.6; /* 增加行高 */
        background-color: var(--error-bg-color); /* 浅红色背景 */
        padding: 10px; /* 内边距 */
        border-radius: var(--border-radius);
        border: 1px solid var(--error-color); /* 红色边框 */
        opacity: 0; /* 初始透明 */
        max-height: 0; /* 初始高度为0 */
        overflow: hidden; /* 隐藏内容 */
        transition: opacity 0.3s ease, max-height 0.3s ease, padding 0.3s ease; /* 添加过渡效果 */
        margin: 0; /* 移除默认margin */
      }

      /* 添加一个类来控制错误信息的显示 */
      .error-display.has-error p {
        opacity: 0; /* 隐藏初始提示 */
        pointer-events: none; /* 防止交互 */
      }
      .error-display.has-error .error-message {
        opacity: 1; /* 显示错误信息 */
        max-height: 500px; /* 允许扩展到足够的高度，可以根据需要调整 */
        padding: 10px; /* 恢复内边距 */
      }
    </style>
    <script src="https://js.sentry-cdn.com/2fd046805ef77e9caf3d719a8cb35316.min.js" crossorigin="anonymous"></script>
  </head>
  <body>
    <h1>JavaScript错误类型演示</h1>

    <div class="button-container">
      <button onclick="triggerSyntaxError()">语法错误 (SyntaxError)</button>
      <button onclick="triggerReferenceError()">引用错误 (ReferenceError)</button>
      <button onclick="triggerTypeError()">类型错误 (TypeError)</button>
      <button onclick="triggerRangeError()">范围错误 (RangeError)</button>
      <button onclick="triggerURIError()">URI错误 (URIError)</button>
      <button onclick="triggerEvalError()">Eval错误 (EvalError)</button>
      <button onclick="triggerDOMException()">DOM异常 (DOMException)</button>
      <button onclick="triggerCustomError()">自定义错误 (Custom Error)</button>
      <button onclick="triggerPromiseError()">Promise错误</button>
      <button onclick="triggerAsyncError()">异步错误</button>
      <button onclick="triggerJSONParseError()">JSON解析错误</button>
      <button onclick="triggerInfiniteLoop()">无限循环</button>
      <button onclick="triggerMemoryLeak()">内存泄漏</button>
      <button onclick="triggerStackOverflow()">栈溢出</button>
    </div>

    <div class="error-display">
      <p>错误信息将显示在这里：</p>
      <pre class="error-message" id="errorMessage"></pre>
    </div>

    <script>
      // 显示错误信息的函数
      function displayError(error) {
        const errorDisplayDiv = document.querySelector(".error-display"); // 获取容器 div
        const errorDisplayPre = document.getElementById("errorMessage"); // 获取 pre 元素

        // 清理可能存在的旧类，以防万一连续点击
        errorDisplayDiv.classList.remove("has-error");

        // 改进错误信息格式
        const formattedStack = (error.stack || "Not available")
          .replace(error.name + ": " + error.message, "") // 移除重复信息
          .trim();
        errorDisplayPre.textContent = `[${error.name || "Error"}]\n${error.message}\n\nStack Trace:\n${formattedStack}`;

        // 使用 setTimeout 确保移除类后的重绘完成，让动画生效
        setTimeout(() => {
          errorDisplayDiv.classList.add("has-error"); // 添加类以显示错误并隐藏提示
        }, 10); // 短暂延迟

        // 将错误输出到控制台
        console.error(error);
        // 如果有Sentry可用，也向Sentry报告错误
        if (window.Sentry) {
          window.Sentry.captureException(error);
        }
      }

      // 语法错误
      function triggerSyntaxError() {
        try {
          eval("alert('Hello world'"); // 缺少闭合括号
        } catch (error) {
          displayError(error);
          // 重新抛出错误
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // 引用错误
      function triggerReferenceError() {
        try {
          // 尝试访问未定义的变量
          console.log(undefinedVariable);
        } catch (error) {
          displayError(error);
          // 重新抛出错误
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // 类型错误
      function triggerTypeError() {
        try {
          // 尝试将非函数当作函数调用
          const obj = {};
          obj.nonExistentFunction();
        } catch (error) {
          displayError(error);
          // 重新抛出错误
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // 范围错误
      function triggerRangeError() {
        try {
          // 尝试创建一个无效精度的数字
          const num = 1.5;
          num.toFixed(-1);
        } catch (error) {
          displayError(error);
          // 重新抛出错误
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // URI错误
      function triggerURIError() {
        try {
          // 尝试解码无效的URI
          decodeURIComponent("%");
        } catch (error) {
          displayError(error);
          // 重新抛出错误
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // Eval错误
      function triggerEvalError() {
        try {
          // 在现代浏览器中很难直接触发EvalError
          // 这里展示的是类似的错误情况
          throw new EvalError("这是一个模拟的EvalError");
        } catch (error) {
          displayError(error);
          // 重新抛出错误
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // DOM异常
      function triggerDOMException() {
        try {
          // 尝试在文档外创建节点
          document.createElement("").appendChild(document.createElement(""));
        } catch (error) {
          displayError(error);
          // 重新抛出错误
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // 自定义错误
      function triggerCustomError() {
        try {
          // 抛出自定义错误
          throw new Error("这是一个自定义的错误信息");
        } catch (error) {
          displayError(error);
          // 重新抛出错误
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // Promise错误
      function triggerPromiseError() {
        // 创建一个会被拒绝的Promise
        new Promise((resolve, reject) => {
          const error = new Error("Promise被拒绝了");
          displayError(error);
          reject(error);
        }).catch((error) => {
          // Promise错误会自动显示在控制台
          setTimeout(() => {
            throw error;
          }, 0);
        });
      }

      // 异步错误
      function triggerAsyncError() {
        // 使用async/await触发错误
        (async function () {
          try {
            const error = new Error("异步操作中的错误");
            await Promise.reject(error);
          } catch (error) {
            displayError(error);
            // 重新抛出错误
            setTimeout(() => {
              throw error;
            }, 0);
          }
        })();
      }

      // JSON解析错误
      function triggerJSONParseError() {
        try {
          // 尝试解析无效的JSON
          JSON.parse("{invalid json}");
        } catch (error) {
          displayError(error);
          // 重新抛出错误
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // 无限循环
      function triggerInfiniteLoop() {
        try {
          // 这里不会真的触发无限循环，而是模拟
          const error = {
            name: "InfiniteLoopError",
            message: "执行时间超过限制，可能存在无限循环",
            stack: "这是一个模拟的无限循环错误，实际执行会冻结浏览器",
          };
          displayError(error);
          // 抛出一个真实的错误对象
          setTimeout(() => {
            const realError = new Error(
              "module/guild/view/guild_challenge_window:297: attempt to index local 'buffData' (a nil value) LogicFrame[100] CSVerion[20224],[ResVersion:20250425014957]"
            );
            realError.name = "InfiniteLoopError";
            throw realError;
          }, 0);
        } catch (error) {
          displayError(error);

          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // 内存泄漏
      function triggerMemoryLeak() {
        try {
          // 这里不会真的触发内存泄漏，而是模拟
          const error = {
            name: "MemoryLeakError",
            message: "内存使用量异常增加，可能存在内存泄漏",
            stack: "这是一个模拟的内存泄漏错误，实际内存泄漏不会立即抛出错误",
          };
          displayError(error);
          // 抛出一个真实的错误对象
          setTimeout(() => {
            const realError = new Error("模拟的内存泄漏错误");
            realError.name = "MemoryLeakError";
            throw realError;
          }, 0);
        } catch (error) {
          displayError(error);
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }

      // 栈溢出
      function triggerStackOverflow() {
        try {
          // 这里不会真的触发栈溢出，而是模拟
          const error = {
            name: "RangeError",
            message: "超出最大调用栈大小",
            stack: "这是一个模拟的栈溢出错误，实际执行会导致浏览器崩溃",
          };
          displayError(error);
          // 抛出一个真实的错误对象
          setTimeout(() => {
            const realError = new RangeError("超出最大调用栈大小");
            throw realError;
          }, 0);

          // 真实栈溢出的代码（已注释，不要取消注释）
          // function recursiveFunction() {
          //     recursiveFunction();
          // }
          // recursiveFunction();
        } catch (error) {
          displayError(error);
          setTimeout(() => {
            throw error;
          }, 0);
        }
      }
    </script>
  </body>
</html>
