#!/bin/bash

# 阶段1：基础环境准备 - 自动化安装脚本
# 作者：DevOps Platform Setup
# 用途：一键完成基础环境配置

set -e  # 遇到错误立即退出

echo "=========================================="
echo "  阶段1：基础环境准备 - 自动化安装"
echo "=========================================="
echo "开始时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 步骤1：系统更新和基础工具安装
install_basic_tools() {
    log_info "步骤1：更新系统并安装基础工具..."
    
    apt update
    apt upgrade -y
    apt install -y curl wget git vim htop tree unzip net-tools
    
    log_info "基础工具安装完成"
}

# 步骤2：检查和配置Docker
configure_docker() {
    log_info "步骤2：配置Docker环境..."
    
    # 检查Docker是否安装
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先通过宝塔面板安装Docker"
        exit 1
    fi
    
    # 检查Docker是否运行
    if ! systemctl is-active --quiet docker; then
        log_warn "Docker未运行，正在启动..."
        systemctl start docker
        systemctl enable docker
    fi
    
    # 配置Docker镜像加速器
    mkdir -p /etc/docker
    
    cat > /etc/docker/daemon.json << EOF
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF
    
    systemctl restart docker
    
    # 安装Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_info "安装Docker Compose..."
        curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
    fi
    
    log_info "Docker配置完成"
}

# 步骤3：创建项目目录结构
create_directories() {
    log_info "步骤3：创建项目目录结构..."
    
    mkdir -p /opt/devops-platform/{gitlab,k3s,configs,scripts,data,logs}
    chown -R root:root /opt/devops-platform
    chmod -R 755 /opt/devops-platform
    
    log_info "目录结构创建完成"
}

# 步骤4：配置网络
configure_network() {
    log_info "步骤4：配置网络..."
    
    # 配置hosts文件
    if ! grep -q "gitlab.local" /etc/hosts; then
        echo "************** gitlab.local" >> /etc/hosts
        echo "************** registry.local" >> /etc/hosts
        echo "************** k8s.local" >> /etc/hosts
    fi
    
    log_info "网络配置完成"
}

# 步骤5：配置防火墙
configure_firewall() {
    log_info "步骤5：配置防火墙..."
    
    # 启用防火墙
    ufw --force enable
    
    # 配置端口
    ufw allow 22/tcp    # SSH
    ufw allow 80/tcp    # HTTP
    ufw allow 443/tcp   # HTTPS
    ufw allow 2222/tcp  # GitLab SSH
    ufw allow 5000/tcp  # Registry
    ufw allow 6443/tcp  # K3s API
    ufw allow 30000:32767/tcp  # K3s NodePort
    
    log_info "防火墙配置完成"
}

# 步骤6：系统优化
optimize_system() {
    log_info "步骤6：系统优化..."
    
    # 系统参数优化
    cat >> /etc/sysctl.conf << EOF

# DevOps Platform Optimizations
vm.max_map_count=262144
fs.file-max=65536
net.core.somaxconn=32768
net.ipv4.ip_forward=1
EOF
    
    sysctl -p
    
    # 文件描述符限制
    cat >> /etc/security/limits.conf << EOF
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
EOF
    
    log_info "系统优化完成"
}

# 步骤7：创建脚本
create_scripts() {
    log_info "步骤7：创建监控和检查脚本..."
    
    # 监控脚本
    cat > /opt/devops-platform/scripts/monitor.sh << 'EOF'
#!/bin/bash

echo "=== 系统资源监控 ==="
echo "时间: $(date)"
echo ""

echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

echo ""
echo "内存使用情况:"
free -h

echo ""
echo "磁盘使用情况:"
df -h

echo ""
echo "Docker容器状态:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "网络连接:"
netstat -tuln | grep LISTEN | head -10
EOF
    
    # 检查脚本
    cat > /opt/devops-platform/scripts/check-env.sh << 'EOF'
#!/bin/bash

echo "=== 基础环境检查 ==="

# 检查Docker
echo "1. Docker状态:"
systemctl is-active docker
docker --version

# 检查Docker Compose
echo "2. Docker Compose:"
docker-compose --version

# 检查网络
echo "3. 网络连通性:"
ping -c 2 ******* > /dev/null && echo "外网连通正常" || echo "外网连通异常"

# 检查端口
echo "4. 端口检查:"
echo "检查关键端口是否被占用..."
netstat -tuln | grep -E "(80|443|2222|5000|6443)" || echo "关键端口未被占用，正常"

# 检查目录
echo "5. 目录结构:"
ls -la /opt/devops-platform/

# 检查系统资源
echo "6. 系统资源:"
echo "CPU核心数: $(nproc)"
echo "内存大小: $(free -h | grep Mem | awk '{print $2}')"
echo "磁盘空间: $(df -h / | tail -1 | awk '{print $4}')"

# 检查系统参数
echo "7. 系统参数:"
echo "vm.max_map_count: $(sysctl vm.max_map_count | cut -d= -f2)"
echo "fs.file-max: $(sysctl fs.file-max | cut -d= -f2)"

echo "=== 检查完成 ==="
EOF
    
    chmod +x /opt/devops-platform/scripts/*.sh
    
    log_info "脚本创建完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装结果..."
    
    echo ""
    echo "=========================================="
    echo "  安装验证"
    echo "=========================================="
    
    /opt/devops-platform/scripts/check-env.sh
    
    echo ""
    echo "=========================================="
    echo "  安装完成"
    echo "=========================================="
    echo "完成时间: $(date)"
    echo ""
    echo "下一步："
    echo "1. 检查上述验证结果是否正常"
    echo "2. 如有问题，请查看错误信息并修复"
    echo "3. 准备进入阶段2：K3s集群搭建"
    echo ""
}

# 主函数
main() {
    check_root
    install_basic_tools
    configure_docker
    create_directories
    configure_network
    configure_firewall
    optimize_system
    create_scripts
    verify_installation
}

# 执行主函数
main "$@"
