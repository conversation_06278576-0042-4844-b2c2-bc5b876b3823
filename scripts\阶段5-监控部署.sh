#!/bin/bash

# 阶段5：应用部署测试 - 自动化安装脚本
# 作者：DevOps Platform Setup
# 用途：一键完成监控系统部署

set -e  # 遇到错误立即退出

echo "=========================================="
echo "  阶段5：应用部署测试 - 自动化安装"
echo "=========================================="
echo "开始时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查K3s集群
    if ! kubectl get nodes &> /dev/null; then
        log_error "K3s集群未运行，请先完成前面的阶段"
        exit 1
    fi
    
    # 检查GitLab
    if ! curl -s http://gitlab.local > /dev/null; then
        log_warn "GitLab无法访问，某些功能可能受影响"
    fi
    
    log_info "前置条件检查通过"
}

# 步骤1：创建监控命名空间
create_monitoring_namespace() {
    log_info "步骤1：创建监控命名空间..."
    
    kubectl create namespace monitoring --dry-run=client -o yaml | kubectl apply -f -
    
    log_info "监控命名空间创建完成"
}

# 步骤2：部署Prometheus
deploy_prometheus() {
    log_info "步骤2：部署Prometheus监控..."
    
    mkdir -p /opt/devops-platform/k3s/monitoring
    
    cat > /opt/devops-platform/k3s/monitoring/prometheus.yaml << 'EOF'
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    scrape_configs:
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']

      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
        - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https

      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
        - role: node
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)

      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
        - role: pod
        relabel_configs:
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
          target_label: __address__
        - action: labelmap
          regex: __meta_kubernetes_pod_label_(.+)
        - source_labels: [__meta_kubernetes_namespace]
          action: replace
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_pod_name]
          action: replace
          target_label: kubernetes_pod_name

      - job_name: 'php-demo-app'
        static_configs:
          - targets: ['php-demo-app-service:80']

      - job_name: 'go-demo-app'
        static_configs:
          - targets: ['go-demo-app-service:8080']
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        args:
          - '--config.file=/etc/prometheus/prometheus.yml'
          - '--storage.tsdb.path=/prometheus/'
          - '--web.console.libraries=/etc/prometheus/console_libraries'
          - '--web.console.templates=/etc/prometheus/consoles'
          - '--storage.tsdb.retention.time=200h'
          - '--web.enable-lifecycle'
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-config-volume
          mountPath: /etc/prometheus/
        - name: prometheus-storage-volume
          mountPath: /prometheus/
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: prometheus-config-volume
        configMap:
          defaultMode: 420
          name: prometheus-config
      - name: prometheus-storage-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: monitoring
spec:
  selector:
    app: prometheus
  ports:
  - port: 9090
    targetPort: 9090
    nodePort: 30090
  type: NodePort
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
- kind: ServiceAccount
  name: prometheus
  namespace: monitoring
EOF
    
    kubectl apply -f /opt/devops-platform/k3s/monitoring/prometheus.yaml
    
    log_info "等待Prometheus启动..."
    kubectl wait --for=condition=ready pod -l app=prometheus -n monitoring --timeout=300s
    
    log_info "Prometheus部署完成"
}

# 步骤3：部署Grafana
deploy_grafana() {
    log_info "步骤3：部署Grafana仪表板..."
    
    cat > /opt/devops-platform/k3s/monitoring/grafana.yaml << 'EOF'
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc
  namespace: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:latest
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          value: "admin123"
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "300m"
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: monitoring
spec:
  selector:
    app: grafana
  ports:
  - port: 3000
    targetPort: 3000
    nodePort: 30300
  type: NodePort
EOF
    
    kubectl apply -f /opt/devops-platform/k3s/monitoring/grafana.yaml
    
    log_info "等待Grafana启动..."
    kubectl wait --for=condition=ready pod -l app=grafana -n monitoring --timeout=300s
    
    log_info "Grafana部署完成"
}

# 步骤4：创建管理脚本
create_management_scripts() {
    log_info "步骤4：创建管理脚本..."

    # 创建健康检查脚本
    cat > /opt/devops-platform/scripts/health-check.sh << 'EOF'
#!/bin/bash

echo "=== 系统健康检查 ==="
echo "检查时间: $(date)"
echo ""

# 检查系统资源
echo "1. 系统资源状态:"
echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "内存使用: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
echo "磁盘使用: $(df / | tail -1 | awk '{print $5}')"
echo ""

# 检查K3s集群
echo "2. K3s集群状态:"
kubectl get nodes --no-headers | while read line; do
    node=$(echo $line | awk '{print $1}')
    status=$(echo $line | awk '{print $2}')
    echo "节点 $node: $status"
done
echo ""

# 检查关键服务
echo "3. 关键服务状态:"
services=("gitlab" "postgres" "redis" "prometheus" "grafana")
for service in "${services[@]}"; do
    if kubectl get pods -A | grep -q $service; then
        status=$(kubectl get pods -A | grep $service | awk '{print $4}' | head -1)
        echo "$service: $status"
    else
        echo "$service: 未部署"
    fi
done
echo ""

# 检查应用状态
echo "4. 应用服务状态:"
apps=("php-demo-app" "go-demo-app")
for app in "${apps[@]}"; do
    if kubectl get pods | grep -q $app; then
        replicas=$(kubectl get deployment $app -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        desired=$(kubectl get deployment $app -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "0")
        echo "$app: $replicas/$desired 就绪"
    else
        echo "$app: 未部署"
    fi
done
echo ""

# 检查网络连通性
echo "5. 网络连通性测试:"
urls=("http://gitlab.local" "http://php-demo.local" "http://go-demo.local")
for url in "${urls[@]}"; do
    if curl -s --max-time 5 $url > /dev/null; then
        echo "$url: 正常"
    else
        echo "$url: 异常"
    fi
done
echo ""

echo "=== 健康检查完成 ==="
EOF

    # 创建监控访问脚本
    cat > /opt/devops-platform/scripts/monitoring-access.sh << 'EOF'
#!/bin/bash

echo "=== 监控系统访问信息 ==="
echo ""
echo "Prometheus监控:"
echo "  URL: http://prometheus.local:30090"
echo "  或: http://**************:30090"
echo ""
echo "Grafana仪表板:"
echo "  URL: http://grafana.local:30300"
echo "  或: http://**************:30300"
echo "  用户名: admin"
echo "  密码: admin123"
echo ""
echo "应用访问:"
echo "  PHP Demo: http://php-demo.local 或 http://**************:30080"
echo "  Go Demo: http://go-demo.local 或 http://**************:30081"
echo ""
echo "GitLab:"
echo "  URL: http://gitlab.local"
echo "  Registry: http://registry.local:5000"
EOF

    # 创建性能测试脚本
    cat > /opt/devops-platform/scripts/performance-test.sh << 'EOF'
#!/bin/bash

echo "=== 性能测试开始 ==="
echo "时间: $(date)"
echo ""

# 检查工具是否安装
if ! command -v ab &> /dev/null; then
    echo "正在安装Apache Bench..."
    apt update && apt install -y apache2-utils
fi

# 测试PHP应用
echo "1. PHP Demo App 性能测试:"
echo "基础连接测试:"
ab -n 100 -c 10 http://**************:30080/ | grep -E "(Requests per second|Time per request|Transfer rate)"

echo ""
echo "压力测试 (1000请求，50并发):"
ab -n 1000 -c 50 http://**************:30080/ | grep -E "(Requests per second|Time per request|Transfer rate|Failed requests)"

echo ""
echo "2. Go Demo App 性能测试:"
echo "基础连接测试:"
ab -n 100 -c 10 http://**************:30081/ | grep -E "(Requests per second|Time per request|Transfer rate)"

echo ""
echo "压力测试 (1000请求，50并发):"
ab -n 1000 -c 50 http://**************:30081/ | grep -E "(Requests per second|Time per request|Transfer rate|Failed requests)"

echo ""
echo "=== 性能测试完成 ==="
EOF

    chmod +x /opt/devops-platform/scripts/health-check.sh
    chmod +x /opt/devops-platform/scripts/monitoring-access.sh
    chmod +x /opt/devops-platform/scripts/performance-test.sh

    log_info "管理脚本创建完成"
}

# 步骤5：配置hosts文件
configure_hosts() {
    log_info "步骤5：配置监控域名..."

    # 添加监控域名
    if ! grep -q "prometheus.local" /etc/hosts; then
        echo "************** prometheus.local" >> /etc/hosts
    fi

    if ! grep -q "grafana.local" /etc/hosts; then
        echo "************** grafana.local" >> /etc/hosts
    fi

    log_info "域名配置完成"
}

# 验证安装
verify_installation() {
    log_info "验证监控系统安装..."

    echo ""
    echo "=========================================="
    echo "  监控系统安装验证"
    echo "=========================================="

    # 检查服务状态
    echo ""
    echo "监控服务状态:"
    kubectl get pods -n monitoring

    echo ""
    echo "服务访问端口:"
    kubectl get svc -n monitoring

    # 测试访问
    echo ""
    echo "访问测试:"
    if curl -s http://**************:30090 > /dev/null; then
        echo "✅ Prometheus访问正常"
    else
        echo "❌ Prometheus访问失败"
    fi

    if curl -s http://**************:30300 > /dev/null; then
        echo "✅ Grafana访问正常"
    else
        echo "❌ Grafana访问失败"
    fi

    echo ""
    echo "=========================================="
    echo "  监控系统安装完成"
    echo "=========================================="
    echo "完成时间: $(date)"
    echo ""
    echo "访问信息:"
    /opt/devops-platform/scripts/monitoring-access.sh
    echo ""
    echo "常用命令:"
    echo "  /opt/devops-platform/scripts/health-check.sh        # 系统健康检查"
    echo "  /opt/devops-platform/scripts/performance-test.sh    # 性能测试"
    echo "  /opt/devops-platform/scripts/monitoring-access.sh   # 查看访问信息"
    echo ""
    echo "下一步: 在Grafana中配置Prometheus数据源和仪表板"
}

# 主函数
main() {
    check_root
    check_prerequisites
    create_monitoring_namespace
    deploy_prometheus
    deploy_grafana
    create_management_scripts
    configure_hosts
    verify_installation
}

# 执行主函数
main "$@"
