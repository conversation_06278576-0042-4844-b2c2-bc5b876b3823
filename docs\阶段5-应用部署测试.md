# 阶段5：应用部署测试

## 📋 本阶段目标
- 部署监控和日志系统
- 进行性能测试和优化
- 配置备份和恢复策略
- 实施安全加固配置
- 验证整个系统的稳定性
- 建立运维监控体系

## 🔧 前置条件检查
在开始之前，请确保前四个阶段已完成：
```bash
# 检查所有服务状态
kubectl get pods -A
curl -I http://gitlab.local
curl -I http://php-demo.local
curl -I http://go-demo.local

# 检查CI/CD流水线
/opt/devops-platform/scripts/gitlab-manager.sh status
/opt/devops-platform/scripts/deploy-manager.sh status
```

## 🚀 详细实施步骤

### 步骤1：部署监控系统

#### 1.1 部署Prometheus监控
```bash
# 创建监控命名空间
kubectl create namespace monitoring

# 创建Prometheus配置
mkdir -p /opt/devops-platform/k3s/monitoring
cat > /opt/devops-platform/k3s/monitoring/prometheus.yaml << 'EOF'
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      # - "first_rules.yml"
      # - "second_rules.yml"

    scrape_configs:
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']

      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
        - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https

      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
        - role: node
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)

      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
        - role: pod
        relabel_configs:
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
          target_label: __address__
        - action: labelmap
          regex: __meta_kubernetes_pod_label_(.+)
        - source_labels: [__meta_kubernetes_namespace]
          action: replace
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_pod_name]
          action: replace
          target_label: kubernetes_pod_name

      - job_name: 'php-demo-app'
        static_configs:
          - targets: ['php-demo-app-service:80']

      - job_name: 'go-demo-app'
        static_configs:
          - targets: ['go-demo-app-service:8080']
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        args:
          - '--config.file=/etc/prometheus/prometheus.yml'
          - '--storage.tsdb.path=/prometheus/'
          - '--web.console.libraries=/etc/prometheus/console_libraries'
          - '--web.console.templates=/etc/prometheus/consoles'
          - '--storage.tsdb.retention.time=200h'
          - '--web.enable-lifecycle'
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-config-volume
          mountPath: /etc/prometheus/
        - name: prometheus-storage-volume
          mountPath: /prometheus/
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: prometheus-config-volume
        configMap:
          defaultMode: 420
          name: prometheus-config
      - name: prometheus-storage-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: monitoring
spec:
  selector:
    app: prometheus
  ports:
  - port: 9090
    targetPort: 9090
    nodePort: 30090
  type: NodePort
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
- kind: ServiceAccount
  name: prometheus
  namespace: monitoring
EOF

# 部署Prometheus
kubectl apply -f /opt/devops-platform/k3s/monitoring/prometheus.yaml

# 等待Prometheus启动
kubectl wait --for=condition=ready pod -l app=prometheus -n monitoring --timeout=300s
```

#### 1.2 部署Grafana可视化
```bash
# 创建Grafana配置
cat > /opt/devops-platform/k3s/monitoring/grafana.yaml << 'EOF'
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc
  namespace: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:latest
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          value: "admin123"
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "300m"
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: monitoring
spec:
  selector:
    app: grafana
  ports:
  - port: 3000
    targetPort: 3000
    nodePort: 30300
  type: NodePort
EOF

# 部署Grafana
kubectl apply -f /opt/devops-platform/k3s/monitoring/grafana.yaml

# 等待Grafana启动
kubectl wait --for=condition=ready pod -l app=grafana -n monitoring --timeout=300s
```

### 步骤2：配置日志收集系统

#### 2.1 部署ELK Stack（简化版）
```bash
# 创建Elasticsearch
cat > /opt/devops-platform/k3s/monitoring/elasticsearch.yaml << 'EOF'
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: elasticsearch-pvc
  namespace: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: elasticsearch
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      containers:
      - name: elasticsearch
        image: docker.elastic.co/elasticsearch/elasticsearch:7.17.0
        ports:
        - containerPort: 9200
        - containerPort: 9300
        env:
        - name: discovery.type
          value: "single-node"
        - name: ES_JAVA_OPTS
          value: "-Xms512m -Xmx512m"
        volumeMounts:
        - name: elasticsearch-storage
          mountPath: /usr/share/elasticsearch/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: elasticsearch-storage
        persistentVolumeClaim:
          claimName: elasticsearch-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch-service
  namespace: monitoring
spec:
  selector:
    app: elasticsearch
  ports:
  - port: 9200
    targetPort: 9200
EOF

# 部署Elasticsearch
kubectl apply -f /opt/devops-platform/k3s/monitoring/elasticsearch.yaml

# 等待Elasticsearch启动
kubectl wait --for=condition=ready pod -l app=elasticsearch -n monitoring --timeout=600s
```

#### 2.2 部署Kibana
```bash
# 创建Kibana
cat > /opt/devops-platform/k3s/monitoring/kibana.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
    spec:
      containers:
      - name: kibana
        image: docker.elastic.co/kibana/kibana:7.17.0
        ports:
        - containerPort: 5601
        env:
        - name: ELASTICSEARCH_HOSTS
          value: "http://elasticsearch-service:9200"
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: kibana-service
  namespace: monitoring
spec:
  selector:
    app: kibana
  ports:
  - port: 5601
    targetPort: 5601
    nodePort: 30601
  type: NodePort
EOF

# 部署Kibana
kubectl apply -f /opt/devops-platform/k3s/monitoring/kibana.yaml

# 等待Kibana启动
kubectl wait --for=condition=ready pod -l app=kibana -n monitoring --timeout=600s
```

### 步骤3：性能测试

#### 3.1 安装性能测试工具
```bash
# 安装Apache Bench
apt update
apt install -y apache2-utils

# 安装wrk（高性能HTTP基准测试工具）
apt install -y build-essential libssl-dev git
cd /tmp
git clone https://github.com/wg/wrk.git
cd wrk
make
cp wrk /usr/local/bin/
```

#### 3.2 创建性能测试脚本
```bash
# 创建性能测试脚本
cat > /opt/devops-platform/scripts/performance-test.sh << 'EOF'
#!/bin/bash

echo "=== 性能测试开始 ==="
echo "时间: $(date)"
echo ""

# 测试PHP应用
echo "1. PHP Demo App 性能测试:"
echo "基础连接测试:"
ab -n 100 -c 10 http://**************:30080/ | grep -E "(Requests per second|Time per request|Transfer rate)"

echo ""
echo "压力测试 (1000请求，50并发):"
ab -n 1000 -c 50 http://**************:30080/ | grep -E "(Requests per second|Time per request|Transfer rate|Failed requests)"

echo ""
echo "2. Go Demo App 性能测试:"
echo "基础连接测试:"
ab -n 100 -c 10 http://**************:30081/ | grep -E "(Requests per second|Time per request|Transfer rate)"

echo ""
echo "压力测试 (1000请求，50并发):"
ab -n 1000 -c 50 http://**************:30081/ | grep -E "(Requests per second|Time per request|Transfer rate|Failed requests)"

echo ""
echo "3. 使用wrk进行高并发测试:"
echo "PHP应用 - 30秒，100连接，10线程:"
wrk -t10 -c100 -d30s http://**************:30080/

echo ""
echo "Go应用 - 30秒，100连接，10线程:"
wrk -t10 -c100 -d30s http://**************:30081/

echo ""
echo "=== 性能测试完成 ==="
EOF

chmod +x /opt/devops-platform/scripts/performance-test.sh
```

### 步骤4：配置备份策略

#### 4.1 创建数据备份脚本
```bash
# 创建备份脚本
cat > /opt/devops-platform/scripts/backup-system.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/devops-platform/backups"
DATE=$(date +%Y%m%d_%H%M%S)

echo "=== 系统备份开始 ==="
echo "时间: $(date)"
echo "备份目录: $BACKUP_DIR/$DATE"
echo ""

# 创建备份目录
mkdir -p $BACKUP_DIR/$DATE/{k3s,gitlab,configs,databases}

# 1. 备份K3s配置
echo "1. 备份K3s配置..."
cp -r /etc/rancher/k3s $BACKUP_DIR/$DATE/k3s/
kubectl get all --all-namespaces -o yaml > $BACKUP_DIR/$DATE/k3s/all-resources.yaml
kubectl get pv,pvc --all-namespaces -o yaml > $BACKUP_DIR/$DATE/k3s/storage-resources.yaml

# 2. 备份GitLab数据
echo "2. 备份GitLab数据..."
kubectl exec -n gitlab-system deployment/gitlab -- gitlab-backup create BACKUP=$DATE 2>/dev/null || echo "GitLab备份失败，请检查GitLab状态"

# 3. 备份PostgreSQL数据
echo "3. 备份PostgreSQL数据..."
kubectl exec -n gitlab-system deployment/postgres -- pg_dump -U gitlab gitlab > $BACKUP_DIR/$DATE/databases/gitlab_db_$DATE.sql 2>/dev/null || echo "PostgreSQL备份失败"

# 4. 备份配置文件
echo "4. 备份配置文件..."
cp -r /opt/devops-platform/configs $BACKUP_DIR/$DATE/ 2>/dev/null || echo "配置文件目录不存在"
cp -r /opt/devops-platform/k3s $BACKUP_DIR/$DATE/configs/
cp -r /opt/devops-platform/gitlab $BACKUP_DIR/$DATE/configs/

# 5. 备份脚本
echo "5. 备份脚本..."
cp -r /opt/devops-platform/scripts $BACKUP_DIR/$DATE/configs/

# 6. 创建备份信息文件
echo "6. 创建备份信息..."
cat > $BACKUP_DIR/$DATE/backup_info.txt << EOL
备份时间: $(date)
备份类型: 完整备份
K3s版本: $(k3s --version | head -1)
GitLab版本: $(kubectl get deployment gitlab -n gitlab-system -o jsonpath='{.spec.template.spec.containers[0].image}' 2>/dev/null || echo "未知")
系统信息: $(uname -a)
磁盘使用: $(df -h /)
内存使用: $(free -h)
EOL

# 7. 压缩备份
echo "7. 压缩备份文件..."
cd $BACKUP_DIR
tar -czf backup_$DATE.tar.gz $DATE/
rm -rf $DATE/

# 8. 清理旧备份（保留最近7天）
echo "8. 清理旧备份..."
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +7 -delete

echo ""
echo "=== 备份完成 ==="
echo "备份文件: $BACKUP_DIR/backup_$DATE.tar.gz"
echo "备份大小: $(du -h $BACKUP_DIR/backup_$DATE.tar.gz | cut -f1)"
EOF

chmod +x /opt/devops-platform/scripts/backup-system.sh
```

#### 4.2 配置定时备份
```bash
# 创建定时备份的cron任务
cat > /tmp/backup-cron << 'EOF'
# 每天凌晨2点执行备份
0 2 * * * /opt/devops-platform/scripts/backup-system.sh >> /opt/devops-platform/logs/backup.log 2>&1

# 每周日凌晨1点清理Docker未使用的镜像
0 1 * * 0 docker system prune -f >> /opt/devops-platform/logs/cleanup.log 2>&1
EOF

# 安装cron任务
crontab /tmp/backup-cron
rm /tmp/backup-cron

# 创建日志目录
mkdir -p /opt/devops-platform/logs

echo "定时备份已配置，每天凌晨2点自动备份"
```

### 步骤5：安全加固配置

#### 5.1 配置网络安全策略
```bash
# 创建网络安全策略
cat > /opt/devops-platform/k3s/security/network-policies.yaml << 'EOF'
# 默认拒绝所有入站流量
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-ingress
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
---
# 允许同命名空间内的通信
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-same-namespace
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: default
---
# 允许从Ingress控制器访问应用
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-ingress-controller
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 8080
---
# GitLab系统网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: gitlab-network-policy
  namespace: gitlab-system
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: gitlab-system
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: gitlab-system
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
EOF

# 应用网络策略（可选，根据安全需求）
# kubectl apply -f /opt/devops-platform/k3s/security/network-policies.yaml
```

#### 5.2 配置Pod安全策略
```bash
# 创建Pod安全策略
cat > /opt/devops-platform/k3s/security/pod-security-policy.yaml << 'EOF'
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: restricted
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: restricted-psp-user
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - restricted
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: restricted-psp-all-serviceaccounts
subjects:
- kind: Group
  name: system:serviceaccounts
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: ClusterRole
  name: restricted-psp-user
  apiGroup: rbac.authorization.k8s.io
EOF

# 应用Pod安全策略（可选）
# kubectl apply -f /opt/devops-platform/k3s/security/pod-security-policy.yaml
```

#### 5.3 配置资源限制
```bash
# 创建资源配额
cat > /opt/devops-platform/k3s/security/resource-quotas.yaml << 'EOF'
apiVersion: v1
kind: ResourceQuota
metadata:
  name: default-quota
  namespace: default
spec:
  hard:
    requests.cpu: "2"
    requests.memory: 4Gi
    limits.cpu: "4"
    limits.memory: 8Gi
    persistentvolumeclaims: "10"
    pods: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: default-limit-range
  namespace: default
spec:
  limits:
  - default:
      cpu: "200m"
      memory: "256Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
EOF

# 应用资源限制
kubectl apply -f /opt/devops-platform/k3s/security/resource-quotas.yaml
```

### 步骤6：创建综合监控脚本

#### 6.1 系统健康检查脚本
```bash
# 创建系统健康检查脚本
cat > /opt/devops-platform/scripts/health-check.sh << 'EOF'
#!/bin/bash

echo "=== 系统健康检查 ==="
echo "检查时间: $(date)"
echo ""

# 检查系统资源
echo "1. 系统资源状态:"
echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "内存使用: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
echo "磁盘使用: $(df / | tail -1 | awk '{print $5}')"
echo ""

# 检查K3s集群
echo "2. K3s集群状态:"
kubectl get nodes --no-headers | while read line; do
    node=$(echo $line | awk '{print $1}')
    status=$(echo $line | awk '{print $2}')
    echo "节点 $node: $status"
done
echo ""

# 检查关键服务
echo "3. 关键服务状态:"
services=("gitlab" "postgres" "redis" "prometheus" "grafana")
for service in "${services[@]}"; do
    if kubectl get pods -A | grep -q $service; then
        status=$(kubectl get pods -A | grep $service | awk '{print $4}' | head -1)
        echo "$service: $status"
    else
        echo "$service: 未部署"
    fi
done
echo ""

# 检查应用状态
echo "4. 应用服务状态:"
apps=("php-demo-app" "go-demo-app")
for app in "${apps[@]}"; do
    if kubectl get pods | grep -q $app; then
        replicas=$(kubectl get deployment $app -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        desired=$(kubectl get deployment $app -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "0")
        echo "$app: $replicas/$desired 就绪"
    else
        echo "$app: 未部署"
    fi
done
echo ""

# 检查网络连通性
echo "5. 网络连通性测试:"
urls=("http://gitlab.local" "http://php-demo.local" "http://go-demo.local")
for url in "${urls[@]}"; do
    if curl -s --max-time 5 $url > /dev/null; then
        echo "$url: 正常"
    else
        echo "$url: 异常"
    fi
done
echo ""

# 检查存储
echo "6. 存储状态:"
kubectl get pv --no-headers | while read line; do
    pv=$(echo $line | awk '{print $1}')
    status=$(echo $line | awk '{print $5}')
    echo "PV $pv: $status"
done
echo ""

# 检查最近的事件
echo "7. 最近的系统事件:"
kubectl get events --sort-by='.lastTimestamp' | tail -5
echo ""

echo "=== 健康检查完成 ==="
EOF

chmod +x /opt/devops-platform/scripts/health-check.sh
```

#### 6.2 创建监控仪表板配置
```bash
# 添加监控访问域名
echo "************** prometheus.local" >> /etc/hosts
echo "************** grafana.local" >> /etc/hosts
echo "************** kibana.local" >> /etc/hosts

# 创建监控访问脚本
cat > /opt/devops-platform/scripts/monitoring-access.sh << 'EOF'
#!/bin/bash

echo "=== 监控系统访问信息 ==="
echo ""
echo "Prometheus监控:"
echo "  URL: http://prometheus.local:30090"
echo "  或: http://**************:30090"
echo ""
echo "Grafana仪表板:"
echo "  URL: http://grafana.local:30300"
echo "  或: http://**************:30300"
echo "  用户名: admin"
echo "  密码: admin123"
echo ""
echo "Kibana日志分析:"
echo "  URL: http://kibana.local:30601"
echo "  或: http://**************:30601"
echo ""
echo "应用访问:"
echo "  PHP Demo: http://php-demo.local 或 http://**************:30080"
echo "  Go Demo: http://go-demo.local 或 http://**************:30081"
echo ""
echo "GitLab:"
echo "  URL: http://gitlab.local"
echo "  Registry: http://registry.local:5000"
EOF

chmod +x /opt/devops-platform/scripts/monitoring-access.sh
```

## ✅ 验证步骤

### 验证1：监控系统验证
```bash
# 检查监控服务状态
kubectl get pods -n monitoring

# 访问Prometheus
curl -I http://**************:30090

# 访问Grafana
curl -I http://**************:30300

# 访问Kibana
curl -I http://**************:30601

# 查看监控访问信息
/opt/devops-platform/scripts/monitoring-access.sh
```

### 验证2：性能测试验证
```bash
# 运行性能测试
/opt/devops-platform/scripts/performance-test.sh

# 检查应用在压力下的表现
kubectl top pods
kubectl get pods -o wide
```

### 验证3：备份系统验证
```bash
# 执行一次完整备份
/opt/devops-platform/scripts/backup-system.sh

# 检查备份文件
ls -la /opt/devops-platform/backups/

# 验证cron任务
crontab -l
```

### 验证4：安全配置验证
```bash
# 检查网络策略
kubectl get networkpolicies -A

# 检查资源配额
kubectl get resourcequota -A
kubectl get limitrange -A

# 检查Pod安全策略
kubectl get podsecuritypolicy
```

### 验证5：系统健康检查
```bash
# 运行完整的健康检查
/opt/devops-platform/scripts/health-check.sh

# 检查所有服务状态
kubectl get all -A

# 检查存储状态
kubectl get pv,pvc -A
```

### 验证6：完整CI/CD流程测试
```bash
# 修改应用代码触发CI/CD
cd /opt/devops-platform/demo-projects/php-demo-app
echo "<?php echo json_encode(['version' => '1.2.0', 'updated' => date('Y-m-d H:i:s')]); ?>" > src/version.php
git add .
git commit -m "Update to version 1.2.0"
git push origin main

# 在GitLab中观察CI/CD流水线
# 手动触发部署
# 验证应用更新

# 检查新版本
curl http://**************:30080/version.php
```

## 📝 本阶段总结

完成本阶段后，您应该有：
- ✅ 完整的监控系统（Prometheus + Grafana）
- ✅ 日志收集系统（ELK Stack）
- ✅ 性能测试工具和脚本
- ✅ 自动化备份策略
- ✅ 安全加固配置
- ✅ 系统健康检查机制
- ✅ 完整的运维监控体系

## 🎯 项目完成总结

### 整体架构回顾
经过5个阶段的搭建，您现在拥有：

1. **基础环境**：优化的Ubuntu系统 + Docker环境
2. **容器编排**：K3s轻量级Kubernetes集群
3. **代码管理**：GitLab代码仓库 + Container Registry
4. **CI/CD流水线**：自动化构建、测试、部署
5. **监控运维**：完整的监控、日志、备份体系

### 核心功能
- ✅ 代码推送自动触发CI/CD流水线
- ✅ 自动化构建Docker镜像
- ✅ 自动化部署到Kubernetes
- ✅ 实时监控和告警
- ✅ 日志收集和分析
- ✅ 定期备份和恢复
- ✅ 安全策略和资源限制

### 访问地址汇总
```bash
# 运行此脚本查看所有访问地址
/opt/devops-platform/scripts/monitoring-access.sh
```

### 常用管理命令
```bash
# 系统状态检查
/opt/devops-platform/scripts/health-check.sh

# 应用管理
/opt/devops-platform/scripts/deploy-manager.sh status

# GitLab管理
/opt/devops-platform/scripts/gitlab-manager.sh status

# K3s集群管理
/opt/devops-platform/scripts/k3s-manager.sh status

# 性能测试
/opt/devops-platform/scripts/performance-test.sh

# 系统备份
/opt/devops-platform/scripts/backup-system.sh
```

## 🔄 后续优化建议

1. **高可用性**：考虑多节点K3s集群
2. **存储优化**：使用外部存储系统
3. **网络优化**：配置负载均衡器
4. **安全增强**：启用HTTPS和证书管理
5. **监控扩展**：添加更多监控指标和告警规则
6. **自动化扩展**：实现自动扩缩容

---

**🎉 恭喜！您已成功搭建了完整的单机DevOps平台！**

**⚠️ 重要提醒**：
1. 定期更新系统和软件包
2. 监控系统资源使用情况
3. 定期检查备份文件完整性
4. 关注安全补丁和更新
5. 根据实际使用情况调整资源配置
