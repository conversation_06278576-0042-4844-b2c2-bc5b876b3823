# 阶段2：K3s集群搭建

## 📋 本阶段目标
- 安装K3s轻量级Kubernetes集群
- 配置kubectl命令行工具
- 部署基础服务和存储
- 验证集群功能正常
- 配置集群网络和安全

## 🔧 前置条件检查
在开始之前，请确保阶段1已完成：
```bash
# 运行环境检查
/opt/devops-platform/scripts/check-env.sh
```

## 🚀 详细实施步骤

### 步骤1：安装K3s

#### 1.1 下载并安装K3s
```bash
# 进入工作目录
cd /opt/devops-platform/k3s

# 下载K3s安装脚本
curl -sfL https://get.k3s.io | sh -s - --write-kubeconfig-mode 644

# 或者使用国内镜像（如果上面的命令很慢）
curl -sfL https://rancher-mirror.rancher.cn/k3s/k3s-install.sh | INSTALL_K3S_MIRROR=cn sh -s - --write-kubeconfig-mode 644
```

#### 1.2 验证K3s安装
```bash
# 检查K3s服务状态
systemctl status k3s

# 检查节点状态
k3s kubectl get nodes

# 检查系统Pod状态
k3s kubectl get pods -A
```

**预期结果**：应该看到一个Ready状态的节点和所有系统Pod都在Running状态。

### 步骤2：配置kubectl工具

#### 2.1 安装kubectl
```bash
# 下载kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"

# 安装kubectl
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# 验证安装
kubectl version --client
```

#### 2.2 配置kubectl访问集群
```bash
# 复制K3s的kubeconfig
mkdir -p ~/.kube
cp /etc/rancher/k3s/k3s.yaml ~/.kube/config
chown $(id -u):$(id -g) ~/.kube/config

# 测试kubectl连接
kubectl get nodes
kubectl get pods -A
```

### 步骤3：配置存储类

#### 3.1 创建本地存储类
```bash
# 创建存储配置目录
mkdir -p /opt/devops-platform/k3s/storage

# 创建本地存储类配置
cat > /opt/devops-platform/k3s/storage/local-storage-class.yaml << EOF
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: rancher.io/local-path
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Delete
EOF

# 应用配置
kubectl apply -f /opt/devops-platform/k3s/storage/local-storage-class.yaml

# 验证存储类
kubectl get storageclass
```

#### 3.2 创建持久化存储目录
```bash
# 创建存储目录
mkdir -p /opt/devops-platform/data/{gitlab,registry,postgres,redis}

# 设置权限
chmod -R 755 /opt/devops-platform/data/
```

### 步骤4：部署基础服务

#### 4.1 创建命名空间
```bash
# 创建命名空间配置
cat > /opt/devops-platform/k3s/namespaces.yaml << EOF
apiVersion: v1
kind: Namespace
metadata:
  name: gitlab-system
---
apiVersion: v1
kind: Namespace
metadata:
  name: devops-tools
---
apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
EOF

# 应用配置
kubectl apply -f /opt/devops-platform/k3s/namespaces.yaml

# 验证命名空间
kubectl get namespaces
```

#### 4.2 部署Ingress控制器（Traefik已内置）
```bash
# 检查Traefik状态
kubectl get pods -n kube-system | grep traefik

# 查看Traefik服务
kubectl get svc -n kube-system | grep traefik

# 检查Traefik配置
kubectl get ingressclass
```

### 步骤5：配置网络和安全

#### 5.1 创建网络策略
```bash
# 创建网络策略目录
mkdir -p /opt/devops-platform/k3s/network

# 创建默认网络策略
cat > /opt/devops-platform/k3s/network/default-network-policy.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-same-namespace
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: default
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: default
EOF

# 应用网络策略（可选，根据安全需求）
# kubectl apply -f /opt/devops-platform/k3s/network/default-network-policy.yaml
```

#### 5.2 配置RBAC权限
```bash
# 创建RBAC配置目录
mkdir -p /opt/devops-platform/k3s/rbac

# 创建GitLab服务账户
cat > /opt/devops-platform/k3s/rbac/gitlab-rbac.yaml << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: gitlab-admin
  namespace: gitlab-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: gitlab-admin
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: gitlab-admin
  namespace: gitlab-system
EOF

# 应用RBAC配置
kubectl apply -f /opt/devops-platform/k3s/rbac/gitlab-rbac.yaml
```

### 步骤6：部署测试应用

#### 6.1 部署测试Pod
```bash
# 创建测试应用
cat > /opt/devops-platform/k3s/test-app.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-nginx
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: test-nginx
  template:
    metadata:
      labels:
        app: test-nginx
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
---
apiVersion: v1
kind: Service
metadata:
  name: test-nginx-service
  namespace: default
spec:
  selector:
    app: test-nginx
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30080
  type: NodePort
EOF

# 部署测试应用
kubectl apply -f /opt/devops-platform/k3s/test-app.yaml

# 检查部署状态
kubectl get pods
kubectl get svc
```

#### 6.2 测试应用访问
```bash
# 测试内部访问
kubectl exec -it deployment/test-nginx -- curl localhost

# 测试外部访问
curl http://**************:30080

# 查看Pod日志
kubectl logs deployment/test-nginx
```

### 步骤7：创建管理脚本

#### 7.1 K3s管理脚本
```bash
# 创建K3s管理脚本
cat > /opt/devops-platform/scripts/k3s-manager.sh << 'EOF'
#!/bin/bash

# K3s集群管理脚本

case "$1" in
    status)
        echo "=== K3s集群状态 ==="
        echo "服务状态:"
        systemctl is-active k3s
        echo ""
        echo "节点状态:"
        kubectl get nodes
        echo ""
        echo "系统Pod状态:"
        kubectl get pods -A | grep -E "(kube-system|local-path)"
        ;;
    
    restart)
        echo "重启K3s服务..."
        systemctl restart k3s
        sleep 10
        echo "等待服务启动..."
        kubectl get nodes
        ;;
    
    logs)
        echo "查看K3s日志..."
        journalctl -u k3s -f
        ;;
    
    clean)
        echo "清理未使用的资源..."
        kubectl delete pods --field-selector=status.phase=Succeeded -A
        kubectl delete pods --field-selector=status.phase=Failed -A
        ;;
    
    backup)
        echo "备份K3s配置..."
        mkdir -p /opt/devops-platform/backups/k3s/$(date +%Y%m%d)
        cp -r /etc/rancher/k3s /opt/devops-platform/backups/k3s/$(date +%Y%m%d)/
        echo "备份完成: /opt/devops-platform/backups/k3s/$(date +%Y%m%d)/"
        ;;
    
    *)
        echo "用法: $0 {status|restart|logs|clean|backup}"
        echo ""
        echo "  status  - 查看集群状态"
        echo "  restart - 重启K3s服务"
        echo "  logs    - 查看服务日志"
        echo "  clean   - 清理失败的Pod"
        echo "  backup  - 备份配置文件"
        ;;
esac
EOF

chmod +x /opt/devops-platform/scripts/k3s-manager.sh
```

#### 7.2 集群监控脚本
```bash
# 创建集群监控脚本
cat > /opt/devops-platform/scripts/k3s-monitor.sh << 'EOF'
#!/bin/bash

echo "=== K3s集群监控 ==="
echo "时间: $(date)"
echo ""

echo "1. 节点状态:"
kubectl get nodes -o wide

echo ""
echo "2. 系统资源使用:"
kubectl top nodes 2>/dev/null || echo "Metrics server未安装"

echo ""
echo "3. 命名空间资源:"
kubectl get pods -A --field-selector=status.phase!=Running 2>/dev/null | head -10

echo ""
echo "4. 存储状态:"
kubectl get pv,pvc -A

echo ""
echo "5. 服务状态:"
kubectl get svc -A | grep -v ClusterIP | head -10

echo ""
echo "6. Ingress状态:"
kubectl get ingress -A

echo ""
echo "7. 最近事件:"
kubectl get events --sort-by='.lastTimestamp' | tail -5
EOF

chmod +x /opt/devops-platform/scripts/k3s-monitor.sh
```

## ✅ 验证步骤

### 验证1：集群基础功能
```bash
# 运行集群状态检查
/opt/devops-platform/scripts/k3s-manager.sh status

# 运行集群监控
/opt/devops-platform/scripts/k3s-monitor.sh
```

### 验证2：网络连通性测试
```bash
# 测试Pod间通信
kubectl run test-pod --image=busybox --rm -it --restart=Never -- nslookup kubernetes.default

# 测试外部访问
curl -I http://**************:30080
```

### 验证3：存储功能测试
```bash
# 创建测试PVC
cat > /tmp/test-pvc.yaml << EOF
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: test-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
EOF

kubectl apply -f /tmp/test-pvc.yaml
kubectl get pvc test-pvc

# 清理测试资源
kubectl delete pvc test-pvc
rm /tmp/test-pvc.yaml
```

## 📝 本阶段总结

完成本阶段后，您应该有：
- ✅ 运行正常的K3s集群
- ✅ 配置完成的kubectl工具
- ✅ 可用的存储类和持久化存储
- ✅ 基础的网络和安全配置
- ✅ 集群管理和监控脚本
- ✅ 验证通过的测试应用

## 🔄 下一阶段预告

**阶段3：GitLab部署配置**
- GitLab容器化部署
- PostgreSQL和Redis配置
- GitLab初始化设置
- GitLab Runner安装配置

---

**⚠️ 重要提醒**：
1. K3s默认使用SQLite，生产环境建议使用外部数据库
2. 确保防火墙已正确配置K3s相关端口
3. 定期备份K3s配置和数据
4. 监控集群资源使用情况
