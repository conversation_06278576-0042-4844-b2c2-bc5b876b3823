#!/bin/bash

# 阶段4：CI/CD流水线搭建 - 自动化安装脚本
# 作者：DevOps Platform Setup
# 用途：一键完成CI/CD流水线搭建

set -e  # 遇到错误立即退出

echo "=========================================="
echo "  阶段4：CI/CD流水线搭建 - 自动化安装"
echo "=========================================="
echo "开始时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查GitLab是否运行
    if ! curl -s http://gitlab.local > /dev/null; then
        log_error "GitLab未运行或无法访问，请先完成阶段3"
        exit 1
    fi
    
    # 检查K3s集群
    if ! kubectl get nodes &> /dev/null; then
        log_error "K3s集群未运行，请先完成阶段2"
        exit 1
    fi
    
    log_info "前置条件检查通过"
}

# 步骤1：创建项目目录
create_project_directories() {
    log_info "步骤1：创建项目目录..."
    
    mkdir -p /opt/devops-platform/demo-projects/{php-demo-app,go-demo-app}
    
    log_info "项目目录创建完成"
}

# 步骤2：创建PHP示例项目
create_php_project() {
    log_info "步骤2：创建PHP示例项目..."
    
    cd /opt/devops-platform/demo-projects/php-demo-app
    
    # 创建项目结构
    mkdir -p {src,tests,docker,k8s}
    
    # 创建PHP应用代码
    cat > src/index.php << 'EOF'
<?php
header('Content-Type: application/json');

$response = [
    'message' => 'Hello from PHP Demo App!',
    'version' => '1.0.0',
    'timestamp' => date('Y-m-d H:i:s'),
    'hostname' => gethostname(),
    'environment' => $_ENV['APP_ENV'] ?? 'production'
];

echo json_encode($response, JSON_PRETTY_PRINT);
?>
EOF
    
    # 创建健康检查端点
    cat > src/health.php << 'EOF'
<?php
header('Content-Type: application/json');

$health = [
    'status' => 'healthy',
    'timestamp' => date('Y-m-d H:i:s'),
    'checks' => [
        'database' => 'ok',
        'cache' => 'ok',
        'disk_space' => 'ok'
    ]
];

echo json_encode($health, JSON_PRETTY_PRINT);
?>
EOF
    
    # 创建composer.json
    cat > composer.json << 'EOF'
{
    "name": "devops-demo/php-demo-app",
    "description": "PHP Demo Application for CI/CD",
    "type": "project",
    "require": {
        "php": ">=7.4"
    },
    "require-dev": {
        "phpunit/phpunit": "^9.0"
    },
    "autoload": {
        "psr-4": {
            "App\\": "src/"
        }
    }
}
EOF
    
    # 创建Dockerfile
    cat > docker/Dockerfile << 'EOF'
# 构建阶段
FROM composer:2 as builder

WORKDIR /app
COPY composer.json composer.lock* ./
RUN composer install --no-dev --optimize-autoloader --no-interaction

# 运行阶段
FROM php:8.1-apache

# 安装必要的扩展
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd pdo pdo_mysql \
    && rm -rf /var/lib/apt/lists/*

# 配置Apache
RUN a2enmod rewrite
COPY docker/apache.conf /etc/apache2/sites-available/000-default.conf

# 复制应用代码
COPY src/ /var/www/html/
COPY --from=builder /app/vendor /var/www/html/vendor

# 设置权限
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

EXPOSE 80

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health.php || exit 1
EOF
    
    # 创建Apache配置
    cat > docker/apache.conf << 'EOF'
<VirtualHost *:80>
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
EOF
    
    log_info "PHP项目创建完成"
}

# 步骤3：创建PHP项目的K8s配置
create_php_k8s_config() {
    log_info "步骤3：创建PHP项目的Kubernetes配置..."
    
    cd /opt/devops-platform/demo-projects/php-demo-app
    
    cat > k8s/deployment.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: php-demo-app
  namespace: default
  labels:
    app: php-demo-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: php-demo-app
  template:
    metadata:
      labels:
        app: php-demo-app
    spec:
      containers:
      - name: php-demo-app
        image: registry.local:5000/devops-demo/php-demo-app:latest
        ports:
        - containerPort: 80
        env:
        - name: APP_ENV
          value: "production"
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health.php
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health.php
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: php-demo-app-service
  namespace: default
spec:
  selector:
    app: php-demo-app
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30080
  type: NodePort
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: php-demo-app-ingress
  namespace: default
  annotations:
    kubernetes.io/ingress.class: traefik
spec:
  rules:
  - host: php-demo.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: php-demo-app-service
            port:
              number: 80
EOF
    
    log_info "PHP Kubernetes配置创建完成"
}

# 步骤4：创建PHP项目的CI配置
create_php_ci_config() {
    log_info "步骤4：创建PHP项目的CI/CD配置..."
    
    cd /opt/devops-platform/demo-projects/php-demo-app
    
    cat > .gitlab-ci.yml << 'EOF'
stages:
  - test
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  IMAGE_TAG: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  LATEST_TAG: $CI_REGISTRY_IMAGE:latest

# 测试阶段
test:
  stage: test
  image: php:8.1-cli
  before_script:
    - apt-get update && apt-get install -y git unzip
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    - composer install
  script:
    - php -l src/index.php
    - php -l src/health.php
    - echo "PHP syntax check passed"
  only:
    - main
    - merge_requests

# 构建阶段
build:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -f docker/Dockerfile -t $IMAGE_TAG .
    - docker tag $IMAGE_TAG $LATEST_TAG
    - docker push $IMAGE_TAG
    - docker push $LATEST_TAG
  only:
    - main

# 部署阶段
deploy:
  stage: deploy
  image: bitnami/kubectl:latest
  before_script:
    - mkdir -p ~/.kube
    - echo "$KUBE_CONFIG" | base64 -d > ~/.kube/config
  script:
    - sed -i "s|registry.local:5000/devops-demo/php-demo-app:latest|$LATEST_TAG|g" k8s/deployment.yaml
    - kubectl apply -f k8s/deployment.yaml
    - kubectl rollout status deployment/php-demo-app
    - kubectl get pods -l app=php-demo-app
  only:
    - main
  when: manual
EOF
    
    log_info "PHP CI/CD配置创建完成"
}

# 主函数
main() {
    check_root
    check_prerequisites
    create_project_directories
    create_php_project
    create_php_k8s_config
    create_php_ci_config
    
    log_info "阶段4第一部分完成，PHP项目已创建"
    log_info "请继续运行Go项目创建脚本或手动创建Go项目"
}

# 执行主函数
main "$@"
