# 阶段3：GitLab部署配置

## 📋 本阶段目标
- 在K3s集群中部署GitLab
- 配置PostgreSQL和Redis数据库
- 设置GitLab初始配置
- 安装和配置GitLab Runner
- 配置Container Registry
- 验证GitLab功能正常

## 🔧 前置条件检查
在开始之前，请确保前两个阶段已完成：
```bash
# 检查K3s集群状态
kubectl get nodes
kubectl get pods -A

# 检查测试应用
curl http://192.168.56.101:30080
```

## 🚀 详细实施步骤

### 步骤1：准备GitLab配置

#### 1.1 创建GitLab配置目录
```bash
# 创建GitLab相关目录
mkdir -p /opt/devops-platform/gitlab/{config,data,logs}
mkdir -p /opt/devops-platform/k3s/gitlab

# 设置权限
chmod -R 755 /opt/devops-platform/gitlab/
```

#### 1.2 创建GitLab配置文件
```bash
# 创建GitLab配置
cat > /opt/devops-platform/gitlab/gitlab.rb << 'EOF'
# GitLab配置文件
external_url 'http://gitlab.local'

# 禁用内置的nginx，使用K3s的Ingress
nginx['enable'] = false
web_server['external_users'] = ['www-data']

# GitLab Workhorse配置
gitlab_workhorse['listen_network'] = "tcp"
gitlab_workhorse['listen_addr'] = "0.0.0.0:8181"

# 数据库配置（使用外部PostgreSQL）
postgresql['enable'] = false
gitlab_rails['db_adapter'] = 'postgresql'
gitlab_rails['db_encoding'] = 'unicode'
gitlab_rails['db_host'] = 'postgres-service.gitlab-system.svc.cluster.local'
gitlab_rails['db_port'] = 5432
gitlab_rails['db_database'] = 'gitlab'
gitlab_rails['db_username'] = 'gitlab'
gitlab_rails['db_password'] = 'gitlab123'

# Redis配置（使用外部Redis）
redis['enable'] = false
gitlab_rails['redis_host'] = 'redis-service.gitlab-system.svc.cluster.local'
gitlab_rails['redis_port'] = 6379

# Container Registry配置
registry_external_url 'http://registry.local:5000'
gitlab_rails['registry_enabled'] = true
registry['enable'] = true
registry['registry_http_addr'] = "0.0.0.0:5000"

# 邮件配置（可选）
gitlab_rails['smtp_enable'] = false

# 备份配置
gitlab_rails['backup_keep_time'] = 604800
gitlab_rails['backup_path'] = "/var/opt/gitlab/backups"

# 性能优化
unicorn['worker_processes'] = 2
sidekiq['max_concurrency'] = 10
EOF
```

### 步骤2：部署PostgreSQL数据库

#### 2.1 创建PostgreSQL配置
```bash
# 创建PostgreSQL部署文件
cat > /opt/devops-platform/k3s/gitlab/postgres.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: gitlab-system
data:
  POSTGRES_DB: gitlab
  POSTGRES_USER: gitlab
  POSTGRES_PASSWORD: gitlab123
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: gitlab-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: gitlab-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:13
        envFrom:
        - configMapRef:
            name: postgres-config
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: gitlab-system
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
EOF

# 部署PostgreSQL
kubectl apply -f /opt/devops-platform/k3s/gitlab/postgres.yaml

# 等待PostgreSQL启动
kubectl wait --for=condition=ready pod -l app=postgres -n gitlab-system --timeout=300s
```

#### 2.2 验证PostgreSQL部署
```bash
# 检查PostgreSQL状态
kubectl get pods -n gitlab-system -l app=postgres

# 测试数据库连接
kubectl exec -it deployment/postgres -n gitlab-system -- psql -U gitlab -d gitlab -c "SELECT version();"
```

### 步骤3：部署Redis缓存

#### 3.1 创建Redis配置
```bash
# 创建Redis部署文件
cat > /opt/devops-platform/k3s/gitlab/redis.yaml << EOF
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: gitlab-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: gitlab-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:6-alpine
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: gitlab-system
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
EOF

# 部署Redis
kubectl apply -f /opt/devops-platform/k3s/gitlab/redis.yaml

# 等待Redis启动
kubectl wait --for=condition=ready pod -l app=redis -n gitlab-system --timeout=300s
```

#### 3.2 验证Redis部署
```bash
# 检查Redis状态
kubectl get pods -n gitlab-system -l app=redis

# 测试Redis连接
kubectl exec -it deployment/redis -n gitlab-system -- redis-cli ping
```

### 步骤4：部署GitLab主服务

#### 4.1 创建GitLab部署配置
```bash
# 创建GitLab部署文件
cat > /opt/devops-platform/k3s/gitlab/gitlab.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: gitlab-config
  namespace: gitlab-system
data:
  gitlab.rb: |
$(cat /opt/devops-platform/gitlab/gitlab.rb | sed 's/^/    /')
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitlab-config-pvc
  namespace: gitlab-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitlab-data-pvc
  namespace: gitlab-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitlab-logs-pvc
  namespace: gitlab-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gitlab
  namespace: gitlab-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gitlab
  template:
    metadata:
      labels:
        app: gitlab
    spec:
      containers:
      - name: gitlab
        image: gitlab/gitlab-ce:latest
        ports:
        - containerPort: 8181
        - containerPort: 22
        - containerPort: 5000
        env:
        - name: GITLAB_OMNIBUS_CONFIG
          valueFrom:
            configMapKeyRef:
              name: gitlab-config
              key: gitlab.rb
        volumeMounts:
        - name: gitlab-config
          mountPath: /etc/gitlab
        - name: gitlab-data
          mountPath: /var/opt/gitlab
        - name: gitlab-logs
          mountPath: /var/log/gitlab
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        readinessProbe:
          httpGet:
            path: /-/readiness
            port: 8181
          initialDelaySeconds: 120
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /-/liveness
            port: 8181
          initialDelaySeconds: 200
          periodSeconds: 30
      volumes:
      - name: gitlab-config
        persistentVolumeClaim:
          claimName: gitlab-config-pvc
      - name: gitlab-data
        persistentVolumeClaim:
          claimName: gitlab-data-pvc
      - name: gitlab-logs
        persistentVolumeClaim:
          claimName: gitlab-logs-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: gitlab-service
  namespace: gitlab-system
spec:
  selector:
    app: gitlab
  ports:
  - name: http
    port: 80
    targetPort: 8181
  - name: ssh
    port: 22
    targetPort: 22
    nodePort: 30022
  - name: registry
    port: 5000
    targetPort: 5000
    nodePort: 30500
  type: NodePort
EOF

# 部署GitLab
kubectl apply -f /opt/devops-platform/k3s/gitlab/gitlab.yaml
```

#### 4.2 等待GitLab启动
```bash
# GitLab启动需要较长时间，请耐心等待
log_info "GitLab正在启动，这可能需要5-10分钟..."

# 监控启动过程
kubectl logs -f deployment/gitlab -n gitlab-system
```

### 步骤5：配置Ingress访问

#### 5.1 创建Ingress配置
```bash
# 创建Ingress配置
cat > /opt/devops-platform/k3s/gitlab/ingress.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gitlab-ingress
  namespace: gitlab-system
  annotations:
    kubernetes.io/ingress.class: traefik
    traefik.ingress.kubernetes.io/router.entrypoints: web
spec:
  rules:
  - host: gitlab.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gitlab-service
            port:
              number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: registry-ingress
  namespace: gitlab-system
  annotations:
    kubernetes.io/ingress.class: traefik
    traefik.ingress.kubernetes.io/router.entrypoints: web
spec:
  rules:
  - host: registry.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gitlab-service
            port:
              number: 5000
EOF

# 应用Ingress配置
kubectl apply -f /opt/devops-platform/k3s/gitlab/ingress.yaml
```

### 步骤6：GitLab初始化配置

#### 6.1 获取初始密码
```bash
# 等待GitLab完全启动
kubectl wait --for=condition=ready pod -l app=gitlab -n gitlab-system --timeout=600s

# 获取root用户的初始密码
kubectl exec -it deployment/gitlab -n gitlab-system -- cat /etc/gitlab/initial_root_password

# 保存密码到文件
kubectl exec -it deployment/gitlab -n gitlab-system -- cat /etc/gitlab/initial_root_password > /opt/devops-platform/gitlab/initial_password.txt
```

#### 6.2 访问GitLab Web界面
```bash
echo "GitLab访问信息："
echo "URL: http://gitlab.local"
echo "用户名: root"
echo "密码: $(cat /opt/devops-platform/gitlab/initial_password.txt | grep Password: | awk '{print $2}')"
echo ""
echo "请在浏览器中访问 http://gitlab.local 进行初始化配置"
```

### 步骤7：安装GitLab Runner

#### 7.1 创建GitLab Runner配置
```bash
# 创建GitLab Runner部署文件
cat > /opt/devops-platform/k3s/gitlab/gitlab-runner.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: gitlab-runner-config
  namespace: gitlab-system
data:
  config.toml: |
    concurrent = 2
    check_interval = 0

    [session_server]
      session_timeout = 1800
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gitlab-runner
  namespace: gitlab-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gitlab-runner
  template:
    metadata:
      labels:
        app: gitlab-runner
    spec:
      serviceAccountName: gitlab-admin
      containers:
      - name: gitlab-runner
        image: gitlab/gitlab-runner:latest
        volumeMounts:
        - name: config
          mountPath: /etc/gitlab-runner
        - name: docker-sock
          mountPath: /var/run/docker.sock
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: config
        configMap:
          name: gitlab-runner-config
      - name: docker-sock
        hostPath:
          path: /var/run/docker.sock
EOF

# 部署GitLab Runner
kubectl apply -f /opt/devops-platform/k3s/gitlab/gitlab-runner.yaml
```

### 步骤8：创建GitLab管理脚本

#### 8.1 GitLab管理脚本
```bash
# 创建GitLab管理脚本
cat > /opt/devops-platform/scripts/gitlab-manager.sh << 'EOF'
#!/bin/bash

case "$1" in
    status)
        echo "=== GitLab服务状态 ==="
        kubectl get pods -n gitlab-system
        echo ""
        kubectl get svc -n gitlab-system
        echo ""
        kubectl get ingress -n gitlab-system
        ;;

    logs)
        echo "查看GitLab日志..."
        kubectl logs -f deployment/gitlab -n gitlab-system
        ;;

    password)
        echo "获取GitLab root初始密码..."
        kubectl exec -it deployment/gitlab -n gitlab-system -- cat /etc/gitlab/initial_root_password 2>/dev/null || echo "密码文件不存在或GitLab未完全启动"
        ;;

    restart)
        echo "重启GitLab服务..."
        kubectl rollout restart deployment/gitlab -n gitlab-system
        kubectl rollout status deployment/gitlab -n gitlab-system
        ;;

    backup)
        echo "备份GitLab数据..."
        kubectl exec -it deployment/gitlab -n gitlab-system -- gitlab-backup create
        ;;

    runner-register)
        if [ -z "$2" ]; then
            echo "用法: $0 runner-register <registration-token>"
            echo "请从GitLab管理界面获取注册令牌"
            exit 1
        fi
        echo "注册GitLab Runner..."
        kubectl exec -it deployment/gitlab-runner -n gitlab-system -- gitlab-runner register \
            --non-interactive \
            --url "http://gitlab-service.gitlab-system.svc.cluster.local" \
            --registration-token "$2" \
            --executor "docker" \
            --docker-image "alpine:latest" \
            --description "k3s-runner" \
            --tag-list "docker,k3s" \
            --run-untagged="true" \
            --locked="false"
        ;;

    *)
        echo "用法: $0 {status|logs|password|restart|backup|runner-register}"
        echo ""
        echo "  status           - 查看GitLab服务状态"
        echo "  logs             - 查看GitLab日志"
        echo "  password         - 获取root初始密码"
        echo "  restart          - 重启GitLab服务"
        echo "  backup           - 备份GitLab数据"
        echo "  runner-register  - 注册GitLab Runner"
        ;;
esac
EOF

chmod +x /opt/devops-platform/scripts/gitlab-manager.sh
```

## ✅ 验证步骤

### 验证1：服务状态检查
```bash
# 检查所有GitLab相关服务
kubectl get pods -n gitlab-system
kubectl get svc -n gitlab-system
kubectl get ingress -n gitlab-system
```

### 验证2：访问测试
```bash
# 测试GitLab Web访问
curl -I http://gitlab.local

# 测试Registry访问
curl -I http://registry.local:5000/v2/
```

### 验证3：数据库连接测试
```bash
# 测试PostgreSQL连接
kubectl exec -it deployment/gitlab -n gitlab-system -- gitlab-rails dbconsole -p

# 测试Redis连接
kubectl exec -it deployment/gitlab -n gitlab-system -- gitlab-rails console -e "puts Gitlab::Redis::Cache.with { |redis| redis.ping }"
```

## 📝 本阶段总结

完成本阶段后，您应该有：
- ✅ 运行正常的PostgreSQL数据库
- ✅ 运行正常的Redis缓存
- ✅ 完全配置的GitLab实例
- ✅ 可访问的GitLab Web界面
- ✅ 配置完成的Container Registry
- ✅ 安装完成的GitLab Runner

## 🔄 下一阶段预告

**阶段4：CI/CD流水线搭建**
- 创建示例PHP项目
- 创建示例Go项目
- 配置CI/CD流水线
- 自动化构建和部署

---

**⚠️ 重要提醒**：
1. GitLab首次启动需要5-10分钟，请耐心等待
2. 请及时修改root用户的初始密码
3. 建议配置HTTPS和域名解析
4. 定期备份GitLab数据和配置
