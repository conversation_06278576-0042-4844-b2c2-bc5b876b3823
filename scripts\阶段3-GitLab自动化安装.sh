#!/bin/bash

# 阶段3：GitLab部署配置 - 自动化安装脚本
# 作者：DevOps Platform Setup
# 用途：一键完成GitLab部署配置

set -e  # 遇到错误立即退出

echo "=========================================="
echo "  阶段3：GitLab部署配置 - 自动化安装"
echo "=========================================="
echo "开始时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查K3s是否运行
    if ! kubectl get nodes &> /dev/null; then
        log_error "K3s集群未运行，请先完成阶段2"
        exit 1
    fi
    
    # 检查gitlab-system命名空间是否存在
    if ! kubectl get namespace gitlab-system &> /dev/null; then
        log_error "gitlab-system命名空间不存在，请先完成阶段2"
        exit 1
    fi
    
    log_info "前置条件检查通过"
}

# 步骤1：准备GitLab配置
prepare_gitlab_config() {
    log_info "步骤1：准备GitLab配置..."
    
    # 创建GitLab相关目录
    mkdir -p /opt/devops-platform/gitlab/{config,data,logs}
    mkdir -p /opt/devops-platform/k3s/gitlab
    chmod -R 755 /opt/devops-platform/gitlab/
    
    # 创建GitLab配置文件
    cat > /opt/devops-platform/gitlab/gitlab.rb << 'EOF'
# GitLab配置文件
external_url 'http://gitlab.local'

# 禁用内置的nginx，使用K3s的Ingress
nginx['enable'] = false
web_server['external_users'] = ['www-data']

# GitLab Workhorse配置
gitlab_workhorse['listen_network'] = "tcp"
gitlab_workhorse['listen_addr'] = "0.0.0.0:8181"

# 数据库配置（使用外部PostgreSQL）
postgresql['enable'] = false
gitlab_rails['db_adapter'] = 'postgresql'
gitlab_rails['db_encoding'] = 'unicode'
gitlab_rails['db_host'] = 'postgres-service.gitlab-system.svc.cluster.local'
gitlab_rails['db_port'] = 5432
gitlab_rails['db_database'] = 'gitlab'
gitlab_rails['db_username'] = 'gitlab'
gitlab_rails['db_password'] = 'gitlab123'

# Redis配置（使用外部Redis）
redis['enable'] = false
gitlab_rails['redis_host'] = 'redis-service.gitlab-system.svc.cluster.local'
gitlab_rails['redis_port'] = 6379

# Container Registry配置
registry_external_url 'http://registry.local:5000'
gitlab_rails['registry_enabled'] = true
registry['enable'] = true
registry['registry_http_addr'] = "0.0.0.0:5000"

# 邮件配置（可选）
gitlab_rails['smtp_enable'] = false

# 备份配置
gitlab_rails['backup_keep_time'] = 604800
gitlab_rails['backup_path'] = "/var/opt/gitlab/backups"

# 性能优化
unicorn['worker_processes'] = 2
sidekiq['max_concurrency'] = 10
EOF
    
    log_info "GitLab配置准备完成"
}

# 步骤2：部署PostgreSQL
deploy_postgresql() {
    log_info "步骤2：部署PostgreSQL数据库..."
    
    cat > /opt/devops-platform/k3s/gitlab/postgres.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: gitlab-system
data:
  POSTGRES_DB: gitlab
  POSTGRES_USER: gitlab
  POSTGRES_PASSWORD: gitlab123
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: gitlab-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: gitlab-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:13
        envFrom:
        - configMapRef:
            name: postgres-config
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: gitlab-system
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
EOF
    
    kubectl apply -f /opt/devops-platform/k3s/gitlab/postgres.yaml
    
    log_info "等待PostgreSQL启动..."
    kubectl wait --for=condition=ready pod -l app=postgres -n gitlab-system --timeout=300s
    
    log_info "PostgreSQL部署完成"
}

# 步骤3：部署Redis
deploy_redis() {
    log_info "步骤3：部署Redis缓存..."
    
    cat > /opt/devops-platform/k3s/gitlab/redis.yaml << EOF
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: gitlab-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: gitlab-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:6-alpine
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: gitlab-system
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
EOF
    
    kubectl apply -f /opt/devops-platform/k3s/gitlab/redis.yaml
    
    log_info "等待Redis启动..."
    kubectl wait --for=condition=ready pod -l app=redis -n gitlab-system --timeout=300s
    
    log_info "Redis部署完成"
}

# 步骤4：部署GitLab主服务
deploy_gitlab() {
    log_info "步骤4：部署GitLab主服务..."

    # 创建GitLab部署文件（由于文件较大，分段创建）
    cat > /opt/devops-platform/k3s/gitlab/gitlab.yaml << 'EOF'
apiVersion: v1
kind: ConfigMap
metadata:
  name: gitlab-config
  namespace: gitlab-system
data:
  gitlab.rb: |
EOF

    # 添加GitLab配置内容
    cat /opt/devops-platform/gitlab/gitlab.rb | sed 's/^/    /' >> /opt/devops-platform/k3s/gitlab/gitlab.yaml

    # 继续添加其他资源定义
    cat >> /opt/devops-platform/k3s/gitlab/gitlab.yaml << 'EOF'
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitlab-config-pvc
  namespace: gitlab-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitlab-data-pvc
  namespace: gitlab-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitlab-logs-pvc
  namespace: gitlab-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gitlab
  namespace: gitlab-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gitlab
  template:
    metadata:
      labels:
        app: gitlab
    spec:
      containers:
      - name: gitlab
        image: gitlab/gitlab-ce:latest
        ports:
        - containerPort: 8181
        - containerPort: 22
        - containerPort: 5000
        env:
        - name: GITLAB_OMNIBUS_CONFIG
          valueFrom:
            configMapKeyRef:
              name: gitlab-config
              key: gitlab.rb
        volumeMounts:
        - name: gitlab-config
          mountPath: /etc/gitlab
        - name: gitlab-data
          mountPath: /var/opt/gitlab
        - name: gitlab-logs
          mountPath: /var/log/gitlab
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        readinessProbe:
          httpGet:
            path: /-/readiness
            port: 8181
          initialDelaySeconds: 120
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /-/liveness
            port: 8181
          initialDelaySeconds: 200
          periodSeconds: 30
      volumes:
      - name: gitlab-config
        persistentVolumeClaim:
          claimName: gitlab-config-pvc
      - name: gitlab-data
        persistentVolumeClaim:
          claimName: gitlab-data-pvc
      - name: gitlab-logs
        persistentVolumeClaim:
          claimName: gitlab-logs-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: gitlab-service
  namespace: gitlab-system
spec:
  selector:
    app: gitlab
  ports:
  - name: http
    port: 80
    targetPort: 8181
  - name: ssh
    port: 22
    targetPort: 22
    nodePort: 30022
  - name: registry
    port: 5000
    targetPort: 5000
    nodePort: 30500
  type: NodePort
EOF

    kubectl apply -f /opt/devops-platform/k3s/gitlab/gitlab.yaml

    log_info "GitLab主服务部署完成"
}

# 步骤5：配置Ingress
configure_ingress() {
    log_info "步骤5：配置Ingress访问..."

    cat > /opt/devops-platform/k3s/gitlab/ingress.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gitlab-ingress
  namespace: gitlab-system
  annotations:
    kubernetes.io/ingress.class: traefik
    traefik.ingress.kubernetes.io/router.entrypoints: web
spec:
  rules:
  - host: gitlab.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gitlab-service
            port:
              number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: registry-ingress
  namespace: gitlab-system
  annotations:
    kubernetes.io/ingress.class: traefik
    traefik.ingress.kubernetes.io/router.entrypoints: web
spec:
  rules:
  - host: registry.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gitlab-service
            port:
              number: 5000
EOF

    kubectl apply -f /opt/devops-platform/k3s/gitlab/ingress.yaml

    log_info "Ingress配置完成"
}

# 步骤6：部署GitLab Runner
deploy_gitlab_runner() {
    log_info "步骤6：部署GitLab Runner..."

    cat > /opt/devops-platform/k3s/gitlab/gitlab-runner.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: gitlab-runner-config
  namespace: gitlab-system
data:
  config.toml: |
    concurrent = 2
    check_interval = 0

    [session_server]
      session_timeout = 1800
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gitlab-runner
  namespace: gitlab-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gitlab-runner
  template:
    metadata:
      labels:
        app: gitlab-runner
    spec:
      serviceAccountName: gitlab-admin
      containers:
      - name: gitlab-runner
        image: gitlab/gitlab-runner:latest
        volumeMounts:
        - name: config
          mountPath: /etc/gitlab-runner
        - name: docker-sock
          mountPath: /var/run/docker.sock
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: config
        configMap:
          name: gitlab-runner-config
      - name: docker-sock
        hostPath:
          path: /var/run/docker.sock
EOF

    kubectl apply -f /opt/devops-platform/k3s/gitlab/gitlab-runner.yaml

    log_info "GitLab Runner部署完成"
}

# 步骤7：创建管理脚本
create_management_scripts() {
    log_info "步骤7：创建GitLab管理脚本..."

    cat > /opt/devops-platform/scripts/gitlab-manager.sh << 'EOF'
#!/bin/bash

case "$1" in
    status)
        echo "=== GitLab服务状态 ==="
        kubectl get pods -n gitlab-system
        echo ""
        kubectl get svc -n gitlab-system
        echo ""
        kubectl get ingress -n gitlab-system
        ;;

    logs)
        echo "查看GitLab日志..."
        kubectl logs -f deployment/gitlab -n gitlab-system
        ;;

    password)
        echo "获取GitLab root初始密码..."
        kubectl exec -it deployment/gitlab -n gitlab-system -- cat /etc/gitlab/initial_root_password 2>/dev/null || echo "密码文件不存在或GitLab未完全启动"
        ;;

    restart)
        echo "重启GitLab服务..."
        kubectl rollout restart deployment/gitlab -n gitlab-system
        kubectl rollout status deployment/gitlab -n gitlab-system
        ;;

    backup)
        echo "备份GitLab数据..."
        kubectl exec -it deployment/gitlab -n gitlab-system -- gitlab-backup create
        ;;

    runner-register)
        if [ -z "$2" ]; then
            echo "用法: $0 runner-register <registration-token>"
            echo "请从GitLab管理界面获取注册令牌"
            exit 1
        fi
        echo "注册GitLab Runner..."
        kubectl exec -it deployment/gitlab-runner -n gitlab-system -- gitlab-runner register \
            --non-interactive \
            --url "http://gitlab-service.gitlab-system.svc.cluster.local" \
            --registration-token "$2" \
            --executor "docker" \
            --docker-image "alpine:latest" \
            --description "k3s-runner" \
            --tag-list "docker,k3s" \
            --run-untagged="true" \
            --locked="false"
        ;;

    *)
        echo "用法: $0 {status|logs|password|restart|backup|runner-register}"
        ;;
esac
EOF

    chmod +x /opt/devops-platform/scripts/gitlab-manager.sh

    log_info "管理脚本创建完成"
}

# 验证安装
verify_installation() {
    log_info "验证GitLab安装..."

    echo ""
    echo "=========================================="
    echo "  GitLab安装验证"
    echo "=========================================="

    # 等待GitLab启动
    log_info "等待GitLab服务启动（这可能需要5-10分钟）..."
    kubectl wait --for=condition=ready pod -l app=gitlab -n gitlab-system --timeout=600s

    # 检查服务状态
    echo ""
    echo "服务状态:"
    kubectl get pods -n gitlab-system

    echo ""
    echo "访问信息:"
    echo "GitLab Web: http://gitlab.local"
    echo "GitLab SSH: ssh://git@**************:30022"
    echo "Registry: http://registry.local:5000"

    # 获取初始密码
    echo ""
    echo "获取root用户初始密码..."
    sleep 30  # 等待GitLab完全启动
    if kubectl exec deployment/gitlab -n gitlab-system -- cat /etc/gitlab/initial_root_password 2>/dev/null; then
        kubectl exec deployment/gitlab -n gitlab-system -- cat /etc/gitlab/initial_root_password > /opt/devops-platform/gitlab/initial_password.txt
        echo ""
        echo "密码已保存到: /opt/devops-platform/gitlab/initial_password.txt"
    else
        echo "初始密码文件尚未生成，请稍后运行以下命令获取："
        echo "/opt/devops-platform/scripts/gitlab-manager.sh password"
    fi

    echo ""
    echo "=========================================="
    echo "  GitLab安装完成"
    echo "=========================================="
    echo "完成时间: $(date)"
    echo ""
    echo "下一步操作："
    echo "1. 在浏览器中访问 http://gitlab.local"
    echo "2. 使用用户名 root 和上面显示的密码登录"
    echo "3. 修改root用户密码"
    echo "4. 创建项目并配置CI/CD"
    echo ""
    echo "常用命令："
    echo "  /opt/devops-platform/scripts/gitlab-manager.sh status    # 查看状态"
    echo "  /opt/devops-platform/scripts/gitlab-manager.sh logs      # 查看日志"
    echo "  /opt/devops-platform/scripts/gitlab-manager.sh password  # 获取密码"
    echo ""
    echo "准备进入阶段4：CI/CD流水线搭建"
}

# 主函数
main() {
    check_root
    check_prerequisites
    prepare_gitlab_config
    deploy_postgresql
    deploy_redis
    deploy_gitlab
    configure_ingress
    deploy_gitlab_runner
    create_management_scripts
    verify_installation
}

# 执行主函数
main "$@"
