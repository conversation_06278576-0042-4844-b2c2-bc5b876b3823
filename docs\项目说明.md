# GitLab + K8s + CI/CD 单机部署方案

## 📋 项目概述

本项目提供在单台Ubuntu服务器上搭建完整的GitLab + Kubernetes + CI/CD开发运维平台的解决方案。

### 🎯 目标架构
- **GitLab**：代码仓库和CI/CD平台
- **K3s**：轻量级Kubernetes集群
- **GitLab Runner**：CI/CD执行器
- **Docker Registry**：镜像仓库
- **应用部署**：PHP和Go应用自动化部署

### 🖥️ 服务器要求
- **最低配置**：4核CPU，8GB内存，50GB存储
- **推荐配置**：8核CPU，16GB内存，100GB存储
- **操作系统**：Ubuntu 18.04+ 
- **网络**：稳定的互联网连接

## 🚀 快速开始

### 方式一：自动化安装（推荐）

1. **执行自动化安装**
   ```bash
   # 给脚本执行权限
   chmod +x scripts/阶段1-自动化安装.sh
   
   # 执行安装（需要root权限）
   sudo ./scripts/阶段1-自动化安装.sh
   ```

2. **检查安装结果**
   ```bash
   # 运行检查脚本
   /opt/devops-platform/scripts/check-env.sh
   ```

### 方式二：手动安装

如果您希望了解每个步骤的详细内容，请参考：
- [阶段1：基础环境准备](阶段1-基础环境准备.md)

## 📚 实施阶段

### ✅ 阶段1：基础环境准备
- 系统更新和基础工具安装
- Docker环境配置
- 项目目录结构创建
- 网络和防火墙配置
- 系统性能优化

### 🔄 阶段2：K3s集群搭建（即将发布）
- K3s安装和配置
- kubectl工具配置
- 基础服务部署
- 集群功能验证

### 🔄 阶段3：GitLab部署配置（即将发布）
- GitLab容器部署
- 初始配置和用户设置
- GitLab Runner安装
- Registry配置

### 🔄 阶段4：CI/CD流水线搭建（即将发布）
- PHP应用CI/CD配置
- Go应用CI/CD配置
- 自动化部署流程
- 测试和验证

### 🔄 阶段5：应用部署测试（即将发布）
- 示例应用部署
- 监控和日志配置
- 性能优化
- 备份策略

## 🛠️ 常用命令

### 系统监控
```bash
# 查看系统资源使用情况
/opt/devops-platform/scripts/monitor.sh

# 查看环境配置状态
/opt/devops-platform/scripts/check-env.sh
```

### Docker管理
```bash
# 查看Docker状态
systemctl status docker

# 查看运行中的容器
docker ps

# 查看Docker镜像
docker images
```

### 防火墙管理
```bash
# 查看防火墙状态
ufw status

# 查看防火墙规则
ufw status numbered
```

## 📁 目录结构

```
/opt/devops-platform/
├── gitlab/          # GitLab相关配置和数据
├── k3s/            # K3s相关配置和数据
├── configs/        # 配置文件
├── scripts/        # 脚本文件
├── data/           # 数据存储
└── logs/           # 日志文件
```

## 🔧 故障排除

### 常见问题

1. **Docker无法启动**
   ```bash
   # 检查Docker状态
   systemctl status docker
   
   # 重启Docker
   systemctl restart docker
   ```

2. **端口被占用**
   ```bash
   # 查看端口占用情况
   netstat -tuln | grep <端口号>
   
   # 查看进程
   lsof -i :<端口号>
   ```

3. **磁盘空间不足**
   ```bash
   # 查看磁盘使用情况
   df -h
   
   # 清理Docker未使用的资源
   docker system prune -a
   ```

### 获取帮助

如果遇到问题：
1. 查看相关日志文件
2. 运行检查脚本确认环境状态
3. 记录完整的错误信息
4. 联系技术支持

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 完成阶段1：基础环境准备
- ✅ 提供自动化安装脚本
- ✅ 提供详细的手动安装文档
- 🔄 阶段2-5开发中...

---

**⚠️ 重要提醒**：
- 建议在测试环境中先验证整个流程
- 生产环境部署前请做好数据备份
- 定期更新系统和软件包
- 关注安全补丁和更新
