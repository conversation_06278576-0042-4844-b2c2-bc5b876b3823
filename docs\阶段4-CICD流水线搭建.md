# 阶段4：CI/CD流水线搭建

## 📋 本阶段目标
- 创建示例PHP和Go项目
- 配置GitLab CI/CD流水线
- 设置自动化构建和部署
- 配置Kubernetes部署文件
- 验证完整的CI/CD流程
- 实现代码推送自动部署

## 🔧 前置条件检查
在开始之前，请确保前三个阶段已完成：
```bash
# 检查GitLab服务状态
kubectl get pods -n gitlab-system
curl -I http://gitlab.local

# 检查GitLab Runner状态
kubectl get pods -n gitlab-system -l app=gitlab-runner
```

## 🚀 详细实施步骤

### 步骤1：GitLab初始配置

#### 1.1 登录GitLab并修改密码
```bash
# 获取初始密码
/opt/devops-platform/scripts/gitlab-manager.sh password

# 在浏览器中访问 http://gitlab.local
# 用户名：root
# 密码：使用上面获取的密码
```

#### 1.2 注册GitLab Runner
```bash
# 在GitLab管理界面获取注册令牌
# 路径：Admin Area -> Overview -> Runners -> Register an instance runner

# 使用获取的令牌注册Runner（替换YOUR_TOKEN为实际令牌）
/opt/devops-platform/scripts/gitlab-manager.sh runner-register YOUR_TOKEN

# 验证Runner注册成功
kubectl logs deployment/gitlab-runner -n gitlab-system
```

#### 1.3 创建项目组和用户
在GitLab Web界面中：
1. 创建新的Group：`devops-demo`
2. 在Group中创建两个项目：
   - `php-demo-app`
   - `go-demo-app`

### 步骤2：创建示例PHP项目

#### 2.1 创建PHP项目目录结构
```bash
# 创建PHP项目目录
mkdir -p /opt/devops-platform/demo-projects/php-demo-app
cd /opt/devops-platform/demo-projects/php-demo-app

# 创建项目结构
mkdir -p {src,tests,docker,k8s}
```

#### 2.2 创建PHP应用代码
```bash
# 创建主应用文件
cat > src/index.php << 'EOF'
<?php
header('Content-Type: application/json');

$response = [
    'message' => 'Hello from PHP Demo App!',
    'version' => '1.0.0',
    'timestamp' => date('Y-m-d H:i:s'),
    'hostname' => gethostname(),
    'environment' => $_ENV['APP_ENV'] ?? 'production'
];

echo json_encode($response, JSON_PRETTY_PRINT);
?>
EOF

# 创建健康检查端点
cat > src/health.php << 'EOF'
<?php
header('Content-Type: application/json');

$health = [
    'status' => 'healthy',
    'timestamp' => date('Y-m-d H:i:s'),
    'checks' => [
        'database' => 'ok',
        'cache' => 'ok',
        'disk_space' => 'ok'
    ]
];

echo json_encode($health, JSON_PRETTY_PRINT);
?>
EOF

# 创建composer.json
cat > composer.json << 'EOF'
{
    "name": "devops-demo/php-demo-app",
    "description": "PHP Demo Application for CI/CD",
    "type": "project",
    "require": {
        "php": ">=7.4"
    },
    "require-dev": {
        "phpunit/phpunit": "^9.0"
    },
    "autoload": {
        "psr-4": {
            "App\\": "src/"
        }
    }
}
EOF
```

#### 2.3 创建Dockerfile
```bash
# 创建多阶段构建的Dockerfile
cat > docker/Dockerfile << 'EOF'
# 构建阶段
FROM composer:2 as builder

WORKDIR /app
COPY composer.json composer.lock* ./
RUN composer install --no-dev --optimize-autoloader --no-interaction

# 运行阶段
FROM php:8.1-apache

# 安装必要的扩展
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd pdo pdo_mysql \
    && rm -rf /var/lib/apt/lists/*

# 配置Apache
RUN a2enmod rewrite
COPY docker/apache.conf /etc/apache2/sites-available/000-default.conf

# 复制应用代码
COPY src/ /var/www/html/
COPY --from=builder /app/vendor /var/www/html/vendor

# 设置权限
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

EXPOSE 80

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health.php || exit 1
EOF

# 创建Apache配置
cat > docker/apache.conf << 'EOF'
<VirtualHost *:80>
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
EOF
```

#### 2.4 创建Kubernetes部署文件
```bash
# 创建Deployment配置
cat > k8s/deployment.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: php-demo-app
  namespace: default
  labels:
    app: php-demo-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: php-demo-app
  template:
    metadata:
      labels:
        app: php-demo-app
    spec:
      containers:
      - name: php-demo-app
        image: registry.local:5000/devops-demo/php-demo-app:latest
        ports:
        - containerPort: 80
        env:
        - name: APP_ENV
          value: "production"
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health.php
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health.php
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: php-demo-app-service
  namespace: default
spec:
  selector:
    app: php-demo-app
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30080
  type: NodePort
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: php-demo-app-ingress
  namespace: default
  annotations:
    kubernetes.io/ingress.class: traefik
spec:
  rules:
  - host: php-demo.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: php-demo-app-service
            port:
              number: 80
EOF
```

#### 2.5 创建GitLab CI配置
```bash
# 创建.gitlab-ci.yml文件
cat > .gitlab-ci.yml << 'EOF'
stages:
  - test
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  IMAGE_TAG: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  LATEST_TAG: $CI_REGISTRY_IMAGE:latest

# 测试阶段
test:
  stage: test
  image: php:8.1-cli
  before_script:
    - apt-get update && apt-get install -y git unzip
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    - composer install
  script:
    - php -l src/index.php
    - php -l src/health.php
    - echo "PHP syntax check passed"
  only:
    - main
    - merge_requests

# 构建阶段
build:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -f docker/Dockerfile -t $IMAGE_TAG .
    - docker tag $IMAGE_TAG $LATEST_TAG
    - docker push $IMAGE_TAG
    - docker push $LATEST_TAG
  only:
    - main

# 部署阶段
deploy:
  stage: deploy
  image: bitnami/kubectl:latest
  before_script:
    - mkdir -p ~/.kube
    - echo "$KUBE_CONFIG" | base64 -d > ~/.kube/config
  script:
    - sed -i "s|registry.local:5000/devops-demo/php-demo-app:latest|$LATEST_TAG|g" k8s/deployment.yaml
    - kubectl apply -f k8s/deployment.yaml
    - kubectl rollout status deployment/php-demo-app
    - kubectl get pods -l app=php-demo-app
  only:
    - main
  when: manual
EOF
```

### 步骤3：创建示例Go项目

#### 3.1 创建Go项目目录结构
```bash
# 创建Go项目目录
mkdir -p /opt/devops-platform/demo-projects/go-demo-app
cd /opt/devops-platform/demo-projects/go-demo-app

# 创建项目结构
mkdir -p {cmd,internal,pkg,docker,k8s}
```

#### 3.2 创建Go应用代码
```bash
# 创建go.mod文件
cat > go.mod << 'EOF'
module go-demo-app

go 1.19

require (
    github.com/gin-gonic/gin v1.9.1
)
EOF

# 创建主应用文件
cat > cmd/main.go << 'EOF'
package main

import (
    "net/http"
    "os"
    "time"

    "github.com/gin-gonic/gin"
)

type Response struct {
    Message     string `json:"message"`
    Version     string `json:"version"`
    Timestamp   string `json:"timestamp"`
    Hostname    string `json:"hostname"`
    Environment string `json:"environment"`
}

type HealthResponse struct {
    Status    string            `json:"status"`
    Timestamp string            `json:"timestamp"`
    Checks    map[string]string `json:"checks"`
}

func main() {
    r := gin.Default()

    // 主页端点
    r.GET("/", func(c *gin.Context) {
        hostname, _ := os.Hostname()
        env := os.Getenv("APP_ENV")
        if env == "" {
            env = "production"
        }

        response := Response{
            Message:     "Hello from Go Demo App!",
            Version:     "1.0.0",
            Timestamp:   time.Now().Format("2006-01-02 15:04:05"),
            Hostname:    hostname,
            Environment: env,
        }

        c.JSON(http.StatusOK, response)
    })

    // 健康检查端点
    r.GET("/health", func(c *gin.Context) {
        health := HealthResponse{
            Status:    "healthy",
            Timestamp: time.Now().Format("2006-01-02 15:04:05"),
            Checks: map[string]string{
                "database":   "ok",
                "cache":      "ok",
                "disk_space": "ok",
            },
        }

        c.JSON(http.StatusOK, health)
    })

    // 启动服务器
    r.Run(":8080")
}
EOF
```

#### 3.3 创建Go项目的Dockerfile
```bash
# 创建多阶段构建的Dockerfile
cat > docker/Dockerfile << 'EOF'
# 构建阶段
FROM golang:1.19-alpine AS builder

WORKDIR /app

# 复制go mod文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY cmd/ ./cmd/
COPY internal/ ./internal/
COPY pkg/ ./pkg/

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/main.go

# 运行阶段
FROM alpine:latest

RUN apk --no-cache add ca-certificates curl
WORKDIR /root/

# 复制构建的二进制文件
COPY --from=builder /app/main .

EXPOSE 8080

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

CMD ["./main"]
EOF

# 创建go.sum文件（空文件，实际使用时会自动生成）
touch go.sum
```

#### 3.4 创建Go项目的Kubernetes部署文件
```bash
# 创建Deployment配置
cat > k8s/deployment.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-demo-app
  namespace: default
  labels:
    app: go-demo-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: go-demo-app
  template:
    metadata:
      labels:
        app: go-demo-app
    spec:
      containers:
      - name: go-demo-app
        image: registry.local:5000/devops-demo/go-demo-app:latest
        ports:
        - containerPort: 8080
        env:
        - name: APP_ENV
          value: "production"
        resources:
          requests:
            memory: "32Mi"
            cpu: "25m"
          limits:
            memory: "64Mi"
            cpu: "50m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: go-demo-app-service
  namespace: default
spec:
  selector:
    app: go-demo-app
  ports:
  - port: 8080
    targetPort: 8080
    nodePort: 30081
  type: NodePort
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: go-demo-app-ingress
  namespace: default
  annotations:
    kubernetes.io/ingress.class: traefik
spec:
  rules:
  - host: go-demo.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: go-demo-app-service
            port:
              number: 8080
EOF
```

#### 3.5 创建Go项目的GitLab CI配置
```bash
# 创建.gitlab-ci.yml文件
cat > .gitlab-ci.yml << 'EOF'
stages:
  - test
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  IMAGE_TAG: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  LATEST_TAG: $CI_REGISTRY_IMAGE:latest

# 测试阶段
test:
  stage: test
  image: golang:1.19
  before_script:
    - go mod download
  script:
    - go fmt ./...
    - go vet ./...
    - go test ./...
    - go build -o main ./cmd/main.go
    - echo "Go tests passed"
  only:
    - main
    - merge_requests

# 构建阶段
build:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -f docker/Dockerfile -t $IMAGE_TAG .
    - docker tag $IMAGE_TAG $LATEST_TAG
    - docker push $IMAGE_TAG
    - docker push $LATEST_TAG
  only:
    - main

# 部署阶段
deploy:
  stage: deploy
  image: bitnami/kubectl:latest
  before_script:
    - mkdir -p ~/.kube
    - echo "$KUBE_CONFIG" | base64 -d > ~/.kube/config
  script:
    - sed -i "s|registry.local:5000/devops-demo/go-demo-app:latest|$LATEST_TAG|g" k8s/deployment.yaml
    - kubectl apply -f k8s/deployment.yaml
    - kubectl rollout status deployment/go-demo-app
    - kubectl get pods -l app=go-demo-app
  only:
    - main
  when: manual
EOF
```

### 步骤4：配置GitLab CI/CD变量

#### 4.1 获取Kubernetes配置
```bash
# 获取K3s的kubeconfig并进行base64编码
cat /etc/rancher/k3s/k3s.yaml | base64 -w 0 > /tmp/kube_config_base64.txt

echo "Kubernetes配置已编码，内容如下："
cat /tmp/kube_config_base64.txt
```

#### 4.2 在GitLab中配置CI/CD变量
在GitLab Web界面中为每个项目配置以下变量：

**路径：Project -> Settings -> CI/CD -> Variables**

1. **KUBE_CONFIG**
   - Type: Variable
   - Key: KUBE_CONFIG
   - Value: 上面生成的base64编码的kubeconfig内容
   - Protected: Yes
   - Masked: No

2. **CI_REGISTRY**
   - Type: Variable
   - Key: CI_REGISTRY
   - Value: registry.local:5000
   - Protected: No
   - Masked: No

3. **CI_REGISTRY_IMAGE**
   - Type: Variable
   - Key: CI_REGISTRY_IMAGE
   - Value: registry.local:5000/devops-demo/项目名
   - Protected: No
   - Masked: No

### 步骤5：推送项目到GitLab

#### 5.1 初始化Git仓库并推送PHP项目
```bash
# 进入PHP项目目录
cd /opt/devops-platform/demo-projects/php-demo-app

# 初始化Git仓库
git init
git add .
git commit -m "Initial commit: PHP demo application"

# 添加远程仓库（替换为实际的GitLab项目URL）
git remote add origin http://gitlab.local/devops-demo/php-demo-app.git

# 推送代码
git push -u origin main
```

#### 5.2 初始化Git仓库并推送Go项目
```bash
# 进入Go项目目录
cd /opt/devops-platform/demo-projects/go-demo-app

# 初始化Git仓库
git init
git add .
git commit -m "Initial commit: Go demo application"

# 添加远程仓库（替换为实际的GitLab项目URL）
git remote add origin http://gitlab.local/devops-demo/go-demo-app.git

# 推送代码
git push -u origin main
```

### 步骤6：配置hosts文件

#### 6.1 添加应用域名解析
```bash
# 添加应用域名到hosts文件
echo "************** php-demo.local" >> /etc/hosts
echo "************** go-demo.local" >> /etc/hosts

# 验证配置
cat /etc/hosts | tail -5
```

### 步骤7：创建CI/CD管理脚本

#### 7.1 创建部署管理脚本
```bash
# 创建部署管理脚本
cat > /opt/devops-platform/scripts/deploy-manager.sh << 'EOF'
#!/bin/bash

case "$1" in
    status)
        echo "=== 应用部署状态 ==="
        echo ""
        echo "PHP Demo App:"
        kubectl get pods -l app=php-demo-app
        kubectl get svc php-demo-app-service
        echo ""
        echo "Go Demo App:"
        kubectl get pods -l app=go-demo-app
        kubectl get svc go-demo-app-service
        echo ""
        echo "Ingress状态:"
        kubectl get ingress
        ;;

    logs)
        if [ -z "$2" ]; then
            echo "用法: $0 logs {php|go}"
            exit 1
        fi

        case "$2" in
            php)
                echo "PHP Demo App 日志:"
                kubectl logs -l app=php-demo-app --tail=50
                ;;
            go)
                echo "Go Demo App 日志:"
                kubectl logs -l app=go-demo-app --tail=50
                ;;
            *)
                echo "无效的应用名称，请使用 php 或 go"
                ;;
        esac
        ;;

    restart)
        if [ -z "$2" ]; then
            echo "用法: $0 restart {php|go|all}"
            exit 1
        fi

        case "$2" in
            php)
                echo "重启PHP Demo App..."
                kubectl rollout restart deployment/php-demo-app
                kubectl rollout status deployment/php-demo-app
                ;;
            go)
                echo "重启Go Demo App..."
                kubectl rollout restart deployment/go-demo-app
                kubectl rollout status deployment/go-demo-app
                ;;
            all)
                echo "重启所有应用..."
                kubectl rollout restart deployment/php-demo-app
                kubectl rollout restart deployment/go-demo-app
                kubectl rollout status deployment/php-demo-app
                kubectl rollout status deployment/go-demo-app
                ;;
            *)
                echo "无效的应用名称，请使用 php、go 或 all"
                ;;
        esac
        ;;

    test)
        echo "=== 应用访问测试 ==="
        echo ""
        echo "测试PHP Demo App:"
        curl -s http://php-demo.local | jq . 2>/dev/null || curl -s http://**************:30080
        echo ""
        echo "测试Go Demo App:"
        curl -s http://go-demo.local | jq . 2>/dev/null || curl -s http://**************:30081
        ;;

    scale)
        if [ -z "$2" ] || [ -z "$3" ]; then
            echo "用法: $0 scale {php|go} <replicas>"
            exit 1
        fi

        case "$2" in
            php)
                echo "扩缩容PHP Demo App到 $3 个副本..."
                kubectl scale deployment/php-demo-app --replicas=$3
                kubectl rollout status deployment/php-demo-app
                ;;
            go)
                echo "扩缩容Go Demo App到 $3 个副本..."
                kubectl scale deployment/go-demo-app --replicas=$3
                kubectl rollout status deployment/go-demo-app
                ;;
            *)
                echo "无效的应用名称，请使用 php 或 go"
                ;;
        esac
        ;;

    *)
        echo "用法: $0 {status|logs|restart|test|scale}"
        echo ""
        echo "  status              - 查看应用部署状态"
        echo "  logs {php|go}       - 查看应用日志"
        echo "  restart {php|go|all} - 重启应用"
        echo "  test                - 测试应用访问"
        echo "  scale {php|go} <n>  - 扩缩容应用"
        echo ""
        echo "示例:"
        echo "  $0 status"
        echo "  $0 logs php"
        echo "  $0 restart all"
        echo "  $0 scale php 3"
        ;;
esac
EOF

chmod +x /opt/devops-platform/scripts/deploy-manager.sh
```

## ✅ 验证步骤

### 验证1：CI/CD流水线测试
```bash
# 检查GitLab Runner状态
kubectl get pods -n gitlab-system -l app=gitlab-runner

# 查看Runner日志
kubectl logs deployment/gitlab-runner -n gitlab-system

# 在GitLab Web界面中触发流水线
# 路径：Project -> CI/CD -> Pipelines -> Run Pipeline
```

### 验证2：应用部署测试
```bash
# 运行部署状态检查
/opt/devops-platform/scripts/deploy-manager.sh status

# 测试应用访问
/opt/devops-platform/scripts/deploy-manager.sh test

# 查看应用日志
/opt/devops-platform/scripts/deploy-manager.sh logs php
/opt/devops-platform/scripts/deploy-manager.sh logs go
```

### 验证3：完整CI/CD流程测试
```bash
# 修改应用代码并推送
cd /opt/devops-platform/demo-projects/php-demo-app
echo "<?php echo 'Updated version 1.1.0'; ?>" > src/version.php
git add .
git commit -m "Add version endpoint"
git push origin main

# 在GitLab中观察CI/CD流水线执行
# 手动触发部署阶段
# 验证应用更新
```

## 📝 本阶段总结

完成本阶段后，您应该有：
- ✅ 完整的PHP示例项目和CI/CD流水线
- ✅ 完整的Go示例项目和CI/CD流水线
- ✅ 自动化的构建、测试、部署流程
- ✅ Kubernetes应用部署配置
- ✅ 应用管理和监控脚本
- ✅ 完整的DevOps工作流

## 🔄 下一阶段预告

**阶段5：应用部署测试**
- 监控和日志系统部署
- 性能优化和调优
- 备份和恢复策略
- 安全加固配置

---

**⚠️ 重要提醒**：
1. 确保GitLab Runner已正确注册
2. CI/CD变量配置正确
3. 镜像仓库访问正常
4. Kubernetes集群资源充足
5. 定期清理未使用的Docker镜像
