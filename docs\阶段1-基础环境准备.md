# 阶段1：基础环境准备

## 📋 本阶段目标
- 优化服务器基础配置
- 安装必要的系统工具
- 配置Docker环境
- 准备网络和存储环境
- 设置防火墙和安全配置

## 🖥️ 服务器信息确认
- **IP地址**：**************
- **系统**：Ubuntu
- **配置**：8核CPU，16GB内存，100GB磁盘
- **已安装**：宝塔面板，Docker服务

## 🚀 详细实施步骤

### 步骤1：系统更新和基础工具安装

#### 1.1 连接服务器
```bash
# 通过SSH连接到服务器
ssh root@**************
# 或者使用宝塔面板的终端功能
```

#### 1.2 系统更新
```bash
# 更新软件包列表
apt update

# 升级系统软件包
apt upgrade -y

# 安装基础工具
apt install -y curl wget git vim htop tree unzip net-tools
```

#### 1.3 检查Docker状态
```bash
# 检查Docker是否正在运行
systemctl status docker

# 检查Docker版本
docker --version

# 测试Docker是否正常工作
docker run hello-world
```

**预期结果**：看到"Hello from Docker!"消息表示Docker工作正常。

### 步骤2：配置Docker环境

#### 2.1 配置Docker镜像加速器（提高下载速度）
```bash
# 创建Docker配置目录
mkdir -p /etc/docker

# 配置镜像加速器
cat > /etc/docker/daemon.json << EOF
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF

# 重启Docker服务
systemctl restart docker

# 验证配置
docker info | grep -A 10 "Registry Mirrors"
```

#### 2.2 安装Docker Compose
```bash
# 下载Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

### 步骤3：创建项目目录结构

#### 3.1 创建主目录
```bash
# 创建项目根目录
mkdir -p /opt/devops-platform
cd /opt/devops-platform

# 创建子目录
mkdir -p {gitlab,k3s,configs,scripts,data,logs}

# 查看目录结构
tree /opt/devops-platform
```

#### 3.2 设置目录权限
```bash
# 设置目录所有者
chown -R root:root /opt/devops-platform

# 设置目录权限
chmod -R 755 /opt/devops-platform
```

### 步骤4：网络配置

#### 4.1 检查当前网络配置
```bash
# 查看网络接口
ip addr show

# 查看路由表
ip route show

# 检查DNS配置
cat /etc/resolv.conf
```

#### 4.2 配置主机名解析
```bash
# 编辑hosts文件，添加本地域名解析
echo "************** gitlab.local" >> /etc/hosts
echo "************** registry.local" >> /etc/hosts
echo "************** k8s.local" >> /etc/hosts

# 验证配置
cat /etc/hosts | tail -3
```

### 步骤5：防火墙配置

#### 5.1 检查防火墙状态
```bash
# 检查ufw状态
ufw status

# 如果防火墙未启用，启用它
ufw --force enable
```

#### 5.2 配置必要的端口
```bash
# GitLab HTTP/HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# GitLab SSH
ufw allow 2222/tcp

# GitLab Registry
ufw allow 5000/tcp

# K3s API Server
ufw allow 6443/tcp

# K3s NodePort范围
ufw allow 30000:32767/tcp

# SSH访问
ufw allow 22/tcp

# 查看防火墙规则
ufw status numbered
```

### 步骤6：系统资源优化

#### 6.1 调整系统参数
```bash
# 添加系统优化参数
cat >> /etc/sysctl.conf << EOF

# DevOps Platform Optimizations
vm.max_map_count=262144
fs.file-max=65536
net.core.somaxconn=32768
net.ipv4.ip_forward=1
EOF

# 应用配置
sysctl -p
```

#### 6.2 调整文件描述符限制
```bash
# 添加文件描述符限制配置
cat >> /etc/security/limits.conf << EOF
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
EOF
```

### 步骤7：创建监控和检查脚本

#### 7.1 系统资源监控脚本
```bash
# 创建监控脚本
cat > /opt/devops-platform/scripts/monitor.sh << 'EOF'
#!/bin/bash

echo "=== 系统资源监控 ==="
echo "时间: $(date)"
echo ""

echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

echo ""
echo "内存使用情况:"
free -h

echo ""
echo "磁盘使用情况:"
df -h

echo ""
echo "Docker容器状态:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "网络连接:"
netstat -tuln | grep LISTEN | head -10
EOF

# 添加执行权限
chmod +x /opt/devops-platform/scripts/monitor.sh
```

#### 7.2 环境检查脚本
```bash
# 创建环境检查脚本
cat > /opt/devops-platform/scripts/check-env.sh << 'EOF'
#!/bin/bash

echo "=== 基础环境检查 ==="

# 检查Docker
echo "1. Docker状态:"
systemctl is-active docker
docker --version

# 检查Docker Compose
echo "2. Docker Compose:"
docker-compose --version

# 检查网络
echo "3. 网络连通性:"
ping -c 2 ******* > /dev/null && echo "外网连通正常" || echo "外网连通异常"

# 检查端口
echo "4. 端口检查:"
echo "检查关键端口是否被占用..."
netstat -tuln | grep -E "(80|443|2222|5000|6443)" || echo "关键端口未被占用，正常"

# 检查目录
echo "5. 目录结构:"
ls -la /opt/devops-platform/

# 检查系统资源
echo "6. 系统资源:"
echo "CPU核心数: $(nproc)"
echo "内存大小: $(free -h | grep Mem | awk '{print $2}')"
echo "磁盘空间: $(df -h / | tail -1 | awk '{print $4}')"

# 检查系统参数
echo "7. 系统参数:"
echo "vm.max_map_count: $(sysctl vm.max_map_count | cut -d= -f2)"
echo "fs.file-max: $(sysctl fs.file-max | cut -d= -f2)"

echo "=== 检查完成 ==="
EOF

chmod +x /opt/devops-platform/scripts/check-env.sh
```

## ✅ 验证步骤

### 执行环境检查
```bash
# 运行环境检查脚本
/opt/devops-platform/scripts/check-env.sh

# 运行监控脚本
/opt/devops-platform/scripts/monitor.sh
```

### 手动验证关键配置
```bash
# 验证Docker配置
docker info | grep -A 5 "Registry Mirrors"

# 验证网络配置
ping -c 2 gitlab.local

# 验证防火墙配置
ufw status | grep -E "(80|443|2222|5000|6443)"

# 验证目录权限
ls -la /opt/devops-platform/
```

## 📝 本阶段总结

完成本阶段后，您应该有：
- ✅ 更新的Ubuntu系统和基础工具
- ✅ 优化配置的Docker环境
- ✅ 完整的项目目录结构
- ✅ 正确的网络和防火墙配置
- ✅ 系统性能优化设置
- ✅ 基础监控脚本

## 🔄 下一阶段预告

**阶段2：K3s集群搭建**
- 安装K3s轻量级Kubernetes
- 配置kubectl工具
- 部署基础服务
- 验证集群功能

---

**⚠️ 重要提醒**：
1. 每个步骤执行后请检查输出结果
2. 如遇到错误，请记录错误信息
3. 建议在执行前做好数据备份
4. 有问题随时询问，不要强行继续

**📞 如需帮助**：
- 如果某个命令执行失败，请提供完整的错误信息
- 如果不确定某个步骤的结果是否正确，可以询问
- 建议逐步执行，不要一次性运行所有命令
