#!/bin/bash

# 阶段2：K3s集群搭建 - 自动化安装脚本
# 作者：DevOps Platform Setup
# 用途：一键完成K3s集群搭建

set -e  # 遇到错误立即退出

echo "=========================================="
echo "  阶段2：K3s集群搭建 - 自动化安装"
echo "=========================================="
echo "开始时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查Docker是否运行
    if ! systemctl is-active --quiet docker; then
        log_error "Docker未运行，请先完成阶段1"
        exit 1
    fi
    
    # 检查基础目录是否存在
    if [ ! -d "/opt/devops-platform" ]; then
        log_error "项目目录不存在，请先完成阶段1"
        exit 1
    fi
    
    log_info "前置条件检查通过"
}

# 步骤1：安装K3s
install_k3s() {
    log_info "步骤1：安装K3s..."
    
    cd /opt/devops-platform/k3s
    
    # 检查K3s是否已安装
    if command -v k3s &> /dev/null; then
        log_warn "K3s已安装，跳过安装步骤"
        return
    fi
    
    # 尝试使用官方源安装
    log_info "尝试从官方源安装K3s..."
    if curl -sfL https://get.k3s.io | sh -s - --write-kubeconfig-mode 644; then
        log_info "K3s安装成功"
    else
        log_warn "官方源安装失败，尝试使用国内镜像..."
        curl -sfL https://rancher-mirror.rancher.cn/k3s/k3s-install.sh | INSTALL_K3S_MIRROR=cn sh -s - --write-kubeconfig-mode 644
    fi
    
    # 等待K3s启动
    log_info "等待K3s服务启动..."
    sleep 30
    
    # 验证安装
    if systemctl is-active --quiet k3s; then
        log_info "K3s服务启动成功"
    else
        log_error "K3s服务启动失败"
        exit 1
    fi
}

# 步骤2：配置kubectl
configure_kubectl() {
    log_info "步骤2：配置kubectl..."
    
    # 检查kubectl是否已安装
    if ! command -v kubectl &> /dev/null; then
        log_info "安装kubectl..."
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
        rm kubectl
    fi
    
    # 配置kubeconfig
    mkdir -p ~/.kube
    cp /etc/rancher/k3s/k3s.yaml ~/.kube/config
    chown $(id -u):$(id -g) ~/.kube/config
    
    # 测试kubectl连接
    if kubectl get nodes &> /dev/null; then
        log_info "kubectl配置成功"
    else
        log_error "kubectl配置失败"
        exit 1
    fi
}

# 步骤3：配置存储
configure_storage() {
    log_info "步骤3：配置存储..."
    
    # 创建存储配置目录
    mkdir -p /opt/devops-platform/k3s/storage
    
    # 创建本地存储类配置
    cat > /opt/devops-platform/k3s/storage/local-storage-class.yaml << EOF
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: rancher.io/local-path
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Delete
EOF
    
    # 应用配置
    kubectl apply -f /opt/devops-platform/k3s/storage/local-storage-class.yaml
    
    # 创建持久化存储目录
    mkdir -p /opt/devops-platform/data/{gitlab,registry,postgres,redis}
    chmod -R 755 /opt/devops-platform/data/
    
    log_info "存储配置完成"
}

# 步骤4：创建命名空间
create_namespaces() {
    log_info "步骤4：创建命名空间..."
    
    cat > /opt/devops-platform/k3s/namespaces.yaml << EOF
apiVersion: v1
kind: Namespace
metadata:
  name: gitlab-system
---
apiVersion: v1
kind: Namespace
metadata:
  name: devops-tools
---
apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
EOF
    
    kubectl apply -f /opt/devops-platform/k3s/namespaces.yaml
    
    log_info "命名空间创建完成"
}

# 步骤5：配置RBAC
configure_rbac() {
    log_info "步骤5：配置RBAC权限..."
    
    mkdir -p /opt/devops-platform/k3s/rbac
    
    cat > /opt/devops-platform/k3s/rbac/gitlab-rbac.yaml << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: gitlab-admin
  namespace: gitlab-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: gitlab-admin
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: gitlab-admin
  namespace: gitlab-system
EOF
    
    kubectl apply -f /opt/devops-platform/k3s/rbac/gitlab-rbac.yaml
    
    log_info "RBAC配置完成"
}

# 步骤6：部署测试应用
deploy_test_app() {
    log_info "步骤6：部署测试应用..."
    
    cat > /opt/devops-platform/k3s/test-app.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-nginx
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: test-nginx
  template:
    metadata:
      labels:
        app: test-nginx
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
---
apiVersion: v1
kind: Service
metadata:
  name: test-nginx-service
  namespace: default
spec:
  selector:
    app: test-nginx
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30080
  type: NodePort
EOF
    
    kubectl apply -f /opt/devops-platform/k3s/test-app.yaml
    
    # 等待Pod启动
    log_info "等待测试应用启动..."
    kubectl wait --for=condition=ready pod -l app=test-nginx --timeout=300s
    
    log_info "测试应用部署完成"
}

# 步骤7：创建管理脚本
create_management_scripts() {
    log_info "步骤7：创建管理脚本..."
    
    # K3s管理脚本
    cat > /opt/devops-platform/scripts/k3s-manager.sh << 'EOF'
#!/bin/bash

case "$1" in
    status)
        echo "=== K3s集群状态 ==="
        echo "服务状态:"
        systemctl is-active k3s
        echo ""
        echo "节点状态:"
        kubectl get nodes
        echo ""
        echo "系统Pod状态:"
        kubectl get pods -A | grep -E "(kube-system|local-path)"
        ;;
    
    restart)
        echo "重启K3s服务..."
        systemctl restart k3s
        sleep 10
        echo "等待服务启动..."
        kubectl get nodes
        ;;
    
    logs)
        echo "查看K3s日志..."
        journalctl -u k3s -f
        ;;
    
    clean)
        echo "清理未使用的资源..."
        kubectl delete pods --field-selector=status.phase=Succeeded -A
        kubectl delete pods --field-selector=status.phase=Failed -A
        ;;
    
    backup)
        echo "备份K3s配置..."
        mkdir -p /opt/devops-platform/backups/k3s/$(date +%Y%m%d)
        cp -r /etc/rancher/k3s /opt/devops-platform/backups/k3s/$(date +%Y%m%d)/
        echo "备份完成: /opt/devops-platform/backups/k3s/$(date +%Y%m%d)/"
        ;;
    
    *)
        echo "用法: $0 {status|restart|logs|clean|backup}"
        ;;
esac
EOF
    
    # K3s监控脚本
    cat > /opt/devops-platform/scripts/k3s-monitor.sh << 'EOF'
#!/bin/bash

echo "=== K3s集群监控 ==="
echo "时间: $(date)"
echo ""

echo "1. 节点状态:"
kubectl get nodes -o wide

echo ""
echo "2. 命名空间资源:"
kubectl get pods -A --field-selector=status.phase!=Running 2>/dev/null | head -10

echo ""
echo "3. 存储状态:"
kubectl get pv,pvc -A

echo ""
echo "4. 服务状态:"
kubectl get svc -A | grep -v ClusterIP | head -10

echo ""
echo "5. 最近事件:"
kubectl get events --sort-by='.lastTimestamp' | tail -5
EOF
    
    chmod +x /opt/devops-platform/scripts/k3s-*.sh
    
    log_info "管理脚本创建完成"
}

# 验证安装
verify_installation() {
    log_info "验证K3s安装..."
    
    echo ""
    echo "=========================================="
    echo "  K3s安装验证"
    echo "=========================================="
    
    # 运行状态检查
    /opt/devops-platform/scripts/k3s-manager.sh status
    
    echo ""
    echo "测试应用访问:"
    if curl -s http://**************:30080 > /dev/null; then
        echo "✅ 测试应用访问正常"
    else
        echo "❌ 测试应用访问失败"
    fi
    
    echo ""
    echo "=========================================="
    echo "  K3s安装完成"
    echo "=========================================="
    echo "完成时间: $(date)"
    echo ""
    echo "访问测试应用: http://**************:30080"
    echo ""
    echo "常用命令:"
    echo "  kubectl get nodes                    # 查看节点"
    echo "  kubectl get pods -A                 # 查看所有Pod"
    echo "  /opt/devops-platform/scripts/k3s-manager.sh status  # 集群状态"
    echo ""
    echo "下一步: 准备进入阶段3：GitLab部署配置"
}

# 主函数
main() {
    check_root
    check_prerequisites
    install_k3s
    configure_kubectl
    configure_storage
    create_namespaces
    configure_rbac
    deploy_test_app
    create_management_scripts
    verify_installation
}

# 执行主函数
main "$@"
